---
name: "Devel (nightly) Release"
on:  # yamllint disable-line rule:truthy
  push:
    branches:
      - "main"
permissions:
  contents: "read"
jobs:
  goreleaser:
    runs-on: "depot-ubuntu-24.04-4"
    permissions:
      contents: "write"
      packages: "write" # publish to GHCR
    steps:
      - uses: "actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683" # v4.2.2
        with:
          fetch-depth: 0
      - uses: "authzed/actions/setup-go@391defc4658e3e4ac6e53ba66da5b90a3b3f80e2" # main
      - name: "Install snapcraft"
        run: |
          sudo snap install snapcraft --channel=8.x/stable --classic
          mkdir -p $HOME/.cache/snapcraft/download
          mkdir -p $HOME/.cache/snapcraft/stage-packages
      - uses: "authzed/actions/docker-login@391defc4658e3e4ac6e53ba66da5b90a3b3f80e2" # main
        with:
          quayio_token: "${{ secrets.QUAYIO_PASSWORD }}"
          github_token: "${{ secrets.GITHUB_TOKEN }}"
          dockerhub_token: "${{ secrets.DOCKERHUB_ACCESS_TOKEN }}"
      - uses: "docker/setup-qemu-action@29109295f81e9208d7d86ff1c6c12d2833863392" # v3.6.0
      - uses: "docker/setup-buildx-action@b5ca514318bd6ebac0fb2aedd5d36ec1b5c232a2" # v3.10.0
      - uses: "goreleaser/goreleaser-action@9c156ee8a17a598857849441385a2041ef570552" # v6.3.0
        with:
          distribution: "goreleaser-pro"
          # Pinned because of a regression in 2.3.0
          version: "2.2.0"
          args: "release -f .goreleaser.nightly.yml --clean --nightly"
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
          GORELEASER_KEY: "${{ secrets.GORELEASER_KEY }}"
