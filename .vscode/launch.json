{
  "configurations": [
    {
      "name": "Serve",
      "type": "go",
      "request": "launch",
      "mode": "debug",
      "program": "${workspaceFolder}/cmd/spicedb/main.go",
      "args": [
        "serve"
      ],
      "envFile": "${workspaceFolder}/.env.local",
    },
    {
      "name": "Migrate",
      "type": "go",
      "request": "launch",
      "mode": "debug",
      "program": "${workspaceFolder}/cmd/spicedb/main.go",
      "args": [
        "migrate",
        "head"
      ],
      "envFile": "${workspaceFolder}/.env.local",
    }
  ]
}