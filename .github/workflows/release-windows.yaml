---
name: "Release for Windows"
on:  # yamllint disable-line rule:truthy
  push:
    tags:
      - "*"
permissions:
  contents: "read"
jobs:
  release-windows:
    runs-on: "windows-latest"
    permissions:
      contents: "write"
      packages: "write" # publish to GHCR
    steps:
      - uses: "actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683" # v4.2.2
        with:
          fetch-depth: 0
      - uses: "authzed/actions/setup-go@391defc4658e3e4ac6e53ba66da5b90a3b3f80e2" # main
      - uses: "nowsprinting/check-version-format-action@c7180d5aa53d69af70c364c047482fc71e133f55" # v4.0.6
        id: "version"
        with:
          prefix: "v"
      - uses: "authzed/actions/docker-login@391defc4658e3e4ac6e53ba66da5b90a3b3f80e2" # main
        with:
          quayio_token: "${{ secrets.QUAYIO_PASSWORD }}"
          github_token: "${{ secrets.GITHUB_TOKEN }}"
          dockerhub_token: "${{ secrets.DOCKERHUB_ACCESS_TOKEN }}"
      - uses: "goreleaser/goreleaser-action@9c156ee8a17a598857849441385a2041ef570552" # v6.3.0
        with:
          distribution: "goreleaser-pro"
          # NOTE: keep in sync with goreleaser version in other job.
          # github actions don't allow yaml anchors.
          version: "v2.3.2"
          args: "release --clean --config=.goreleaser.windows.yml"
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
          GORELEASER_KEY: "${{ secrets.GORELEASER_KEY }}"
          CHOCOLATEY_API_KEY: "${{ secrets.CHOCOLATEY_API_KEY }}"
