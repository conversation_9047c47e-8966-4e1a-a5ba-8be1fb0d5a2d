---
name: "Security"
on:  # yamllint disable-line rule:truthy
  push:
    branches:
      - "!dependabot/*"
      - "main"
  pull_request:
    branches: ["*"]
  merge_group:
    types:
      - "checks_requested"
permissions:
  contents: "read"
env:
  DOCKERHUB_PUBLIC_ACCESS_TOKEN: "************************************"
  DOCKERHUB_PUBLIC_USER: "spicedbgithubactions"
jobs:
  codeql:
    name: "CodeQL Analyze"
    if: "${{ github.event_name == 'pull_request' }}"  # workaround to https://github.com/github/codeql-action/issues/1537
    runs-on: "depot-ubuntu-24.04-8"
    timeout-minutes: "${{ (matrix.language == 'swift' && 120) || 360 }}"
    permissions:
      # required for all workflows
      security-events: "write"

      # only required for workflows in private repositories
      actions: "read"
      contents: "read"

    strategy:
      fail-fast: false
      matrix:
        language: ["go"]

    steps:
      - uses: "actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683" # v4.2.2
      - uses: "authzed/actions/setup-go@391defc4658e3e4ac6e53ba66da5b90a3b3f80e2" # main
      - uses: "authzed/actions/codeql@391defc4658e3e4ac6e53ba66da5b90a3b3f80e2" # main"

  trivy:
    name: "Analyze Code and Docker Image with Trivvy"
    runs-on: "depot-ubuntu-24.04"
    steps:
      - uses: "actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683" # v4.2.2
        with:
          # Only a single commit is fetched by default, for the ref/SHA that triggered the workflow. Set fetch-depth: 0
          # to fetch all history for all branches and tags. Refer here to learn which commit $GITHUB_SHA
          # points to for different events.
          #
          # this is used so goreleaser generates the right version out of the tags, which we need so that
          # trivy does not flag an old SpiceDB version
          fetch-depth: 0
      - uses: "authzed/actions/setup-go@391defc4658e3e4ac6e53ba66da5b90a3b3f80e2" # main
      - uses: "docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772" # v3.4.0
        with:
          username: "${{ env.DOCKERHUB_PUBLIC_USER }}"
          password: "${{ env.DOCKERHUB_PUBLIC_ACCESS_TOKEN }}"
      - name: "Install snapcraft"
        run: |
          sudo snap install snapcraft --channel=8.x/stable --classic
          mkdir -p $HOME/.cache/snapcraft/download
          mkdir -p $HOME/.cache/snapcraft/stage-packages
      - uses: "aquasecurity/trivy-action@26d71e622b84d103f86fb33a5a42c558e11f4ae0" # master
        with:
          scan-type: "fs"
          ignore-unfixed: true
          format: "table"
          exit-code: "1"
          severity: "CRITICAL,HIGH,MEDIUM"
        env:
          TRIVY_DB_REPOSITORY: "public.ecr.aws/aquasecurity/trivy-db"
          TRIVY_JAVA_DB_REPOSITORY: "public.ecr.aws/aquasecurity/trivy-java-db"
      - uses: "goreleaser/goreleaser-action@9c156ee8a17a598857849441385a2041ef570552" # v6.3.0
        id: "goreleaser"
        with:
          distribution: "goreleaser-pro"
          version: "2.3.2"
          args: "release --clean --split --snapshot --single-target"
        env:
          GORELEASER_KEY: "${{ secrets.GORELEASER_KEY }}"
      - name: "Obtain container image to scan"
        run: 'echo "IMAGE_VERSION=$(jq .version dist/linux_amd64_v1/metadata.json --raw-output)" >> $GITHUB_ENV'
      - name: "run trivy on release image"
        run: "docker run -v /var/run/docker.sock:/var/run/docker.sock aquasec/trivy image --format table --exit-code 1 --ignore-unfixed --vuln-type os,library --no-progress --severity CRITICAL,HIGH,MEDIUM authzed/spicedb:v${{ env.IMAGE_VERSION }}-amd64 --db-repository public.ecr.aws/aquasecurity/trivy-db --java-db-repository public.ecr.aws/aquasecurity/trivy-java-db"
