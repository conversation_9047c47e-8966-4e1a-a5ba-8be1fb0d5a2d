---
name: "Build WASM"
on:  # yamllint disable-line rule:truthy
  release:
    types: ["created"]
permissions:
  contents: "read"
jobs:
  build:
    name: "Build WASM"
    runs-on: "depot-ubuntu-24.04-small"
    permissions:
      contents: "write"
    steps:
      - uses: "actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683" # v4.2.2
        with:
          ref: "${{ env.GITHUB_SHA }}"
      - uses: "authzed/actions/setup-go@391defc4658e3e4ac6e53ba66da5b90a3b3f80e2" # main
      - name: "Build WASM"
        run: "go run mage.go build:wasm"
      - uses: "shogo82148/actions-upload-release-asset@d22998fda4c1407f60d1ab48cd6fe67f360f34de" # v1.8.0
        with:
          upload_url: "${{ github.event.release.upload_url }}"
          asset_path: "dist/*"
