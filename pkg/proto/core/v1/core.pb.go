// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: core/v1/core.proto

package corev1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RelationTupleUpdate_Operation int32

const (
	RelationTupleUpdate_UNKNOWN RelationTupleUpdate_Operation = 0
	RelationTupleUpdate_CREATE  RelationTupleUpdate_Operation = 1
	RelationTupleUpdate_TOUCH   RelationTupleUpdate_Operation = 2
	RelationTupleUpdate_DELETE  RelationTupleUpdate_Operation = 3
)

// Enum value maps for RelationTupleUpdate_Operation.
var (
	RelationTupleUpdate_Operation_name = map[int32]string{
		0: "UNKNOWN",
		1: "CREATE",
		2: "TOUCH",
		3: "DELETE",
	}
	RelationTupleUpdate_Operation_value = map[string]int32{
		"UNKNOWN": 0,
		"CREATE":  1,
		"TOUCH":   2,
		"DELETE":  3,
	}
)

func (x RelationTupleUpdate_Operation) Enum() *RelationTupleUpdate_Operation {
	p := new(RelationTupleUpdate_Operation)
	*p = x
	return p
}

func (x RelationTupleUpdate_Operation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RelationTupleUpdate_Operation) Descriptor() protoreflect.EnumDescriptor {
	return file_core_v1_core_proto_enumTypes[0].Descriptor()
}

func (RelationTupleUpdate_Operation) Type() protoreflect.EnumType {
	return &file_core_v1_core_proto_enumTypes[0]
}

func (x RelationTupleUpdate_Operation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RelationTupleUpdate_Operation.Descriptor instead.
func (RelationTupleUpdate_Operation) EnumDescriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{8, 0}
}

type SetOperationUserset_Operation int32

const (
	SetOperationUserset_INVALID      SetOperationUserset_Operation = 0
	SetOperationUserset_UNION        SetOperationUserset_Operation = 1
	SetOperationUserset_INTERSECTION SetOperationUserset_Operation = 2
	SetOperationUserset_EXCLUSION    SetOperationUserset_Operation = 3
)

// Enum value maps for SetOperationUserset_Operation.
var (
	SetOperationUserset_Operation_name = map[int32]string{
		0: "INVALID",
		1: "UNION",
		2: "INTERSECTION",
		3: "EXCLUSION",
	}
	SetOperationUserset_Operation_value = map[string]int32{
		"INVALID":      0,
		"UNION":        1,
		"INTERSECTION": 2,
		"EXCLUSION":    3,
	}
)

func (x SetOperationUserset_Operation) Enum() *SetOperationUserset_Operation {
	p := new(SetOperationUserset_Operation)
	*p = x
	return p
}

func (x SetOperationUserset_Operation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SetOperationUserset_Operation) Descriptor() protoreflect.EnumDescriptor {
	return file_core_v1_core_proto_enumTypes[1].Descriptor()
}

func (SetOperationUserset_Operation) Type() protoreflect.EnumType {
	return &file_core_v1_core_proto_enumTypes[1]
}

func (x SetOperationUserset_Operation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SetOperationUserset_Operation.Descriptor instead.
func (SetOperationUserset_Operation) EnumDescriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{10, 0}
}

type ReachabilityEntrypoint_ReachabilityEntrypointKind int32

const (
	// *
	// RELATION_ENTRYPOINT indicates an entrypoint where the subject object can be directly
	// found for a relationship.
	ReachabilityEntrypoint_RELATION_ENTRYPOINT ReachabilityEntrypoint_ReachabilityEntrypointKind = 0
	// *
	// COMPUTED_USERSET_ENTRYPOINT indicates an entrypoint where the subject's relation is
	// "rewritten" via a `computed_userset` to the target permission's operation node.
	ReachabilityEntrypoint_COMPUTED_USERSET_ENTRYPOINT ReachabilityEntrypoint_ReachabilityEntrypointKind = 1
	// *
	// TUPLESET_TO_USERSET_ENTRYPOINT indicates an entrypoint where the subject's relation is
	// walked via a `tupleset_to_userset` in the target permission's operation node.
	ReachabilityEntrypoint_TUPLESET_TO_USERSET_ENTRYPOINT ReachabilityEntrypoint_ReachabilityEntrypointKind = 2
)

// Enum value maps for ReachabilityEntrypoint_ReachabilityEntrypointKind.
var (
	ReachabilityEntrypoint_ReachabilityEntrypointKind_name = map[int32]string{
		0: "RELATION_ENTRYPOINT",
		1: "COMPUTED_USERSET_ENTRYPOINT",
		2: "TUPLESET_TO_USERSET_ENTRYPOINT",
	}
	ReachabilityEntrypoint_ReachabilityEntrypointKind_value = map[string]int32{
		"RELATION_ENTRYPOINT":            0,
		"COMPUTED_USERSET_ENTRYPOINT":    1,
		"TUPLESET_TO_USERSET_ENTRYPOINT": 2,
	}
)

func (x ReachabilityEntrypoint_ReachabilityEntrypointKind) Enum() *ReachabilityEntrypoint_ReachabilityEntrypointKind {
	p := new(ReachabilityEntrypoint_ReachabilityEntrypointKind)
	*p = x
	return p
}

func (x ReachabilityEntrypoint_ReachabilityEntrypointKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReachabilityEntrypoint_ReachabilityEntrypointKind) Descriptor() protoreflect.EnumDescriptor {
	return file_core_v1_core_proto_enumTypes[2].Descriptor()
}

func (ReachabilityEntrypoint_ReachabilityEntrypointKind) Type() protoreflect.EnumType {
	return &file_core_v1_core_proto_enumTypes[2]
}

func (x ReachabilityEntrypoint_ReachabilityEntrypointKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReachabilityEntrypoint_ReachabilityEntrypointKind.Descriptor instead.
func (ReachabilityEntrypoint_ReachabilityEntrypointKind) EnumDescriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{18, 0}
}

type ReachabilityEntrypoint_EntrypointResultStatus int32

const (
	// *
	// REACHABLE_CONDITIONAL_RESULT indicates that the entrypoint is under one or more intersections
	// or exclusion operations, indicating that any reachable object *may* be a result, conditional
	// on the parent non-union operation(s).
	ReachabilityEntrypoint_REACHABLE_CONDITIONAL_RESULT ReachabilityEntrypoint_EntrypointResultStatus = 0
	// *
	// DIRECT_OPERATION_RESULT indicates that the entrypoint exists solely under zero or more
	// union operations, making any reachable object also a *result* of the relation or permission.
	ReachabilityEntrypoint_DIRECT_OPERATION_RESULT ReachabilityEntrypoint_EntrypointResultStatus = 1
)

// Enum value maps for ReachabilityEntrypoint_EntrypointResultStatus.
var (
	ReachabilityEntrypoint_EntrypointResultStatus_name = map[int32]string{
		0: "REACHABLE_CONDITIONAL_RESULT",
		1: "DIRECT_OPERATION_RESULT",
	}
	ReachabilityEntrypoint_EntrypointResultStatus_value = map[string]int32{
		"REACHABLE_CONDITIONAL_RESULT": 0,
		"DIRECT_OPERATION_RESULT":      1,
	}
)

func (x ReachabilityEntrypoint_EntrypointResultStatus) Enum() *ReachabilityEntrypoint_EntrypointResultStatus {
	p := new(ReachabilityEntrypoint_EntrypointResultStatus)
	*p = x
	return p
}

func (x ReachabilityEntrypoint_EntrypointResultStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReachabilityEntrypoint_EntrypointResultStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_core_v1_core_proto_enumTypes[3].Descriptor()
}

func (ReachabilityEntrypoint_EntrypointResultStatus) Type() protoreflect.EnumType {
	return &file_core_v1_core_proto_enumTypes[3]
}

func (x ReachabilityEntrypoint_EntrypointResultStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReachabilityEntrypoint_EntrypointResultStatus.Descriptor instead.
func (ReachabilityEntrypoint_EntrypointResultStatus) EnumDescriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{18, 1}
}

type FunctionedTupleToUserset_Function int32

const (
	FunctionedTupleToUserset_FUNCTION_UNSPECIFIED FunctionedTupleToUserset_Function = 0
	FunctionedTupleToUserset_FUNCTION_ANY         FunctionedTupleToUserset_Function = 1
	FunctionedTupleToUserset_FUNCTION_ALL         FunctionedTupleToUserset_Function = 2
)

// Enum value maps for FunctionedTupleToUserset_Function.
var (
	FunctionedTupleToUserset_Function_name = map[int32]string{
		0: "FUNCTION_UNSPECIFIED",
		1: "FUNCTION_ANY",
		2: "FUNCTION_ALL",
	}
	FunctionedTupleToUserset_Function_value = map[string]int32{
		"FUNCTION_UNSPECIFIED": 0,
		"FUNCTION_ANY":         1,
		"FUNCTION_ALL":         2,
	}
)

func (x FunctionedTupleToUserset_Function) Enum() *FunctionedTupleToUserset_Function {
	p := new(FunctionedTupleToUserset_Function)
	*p = x
	return p
}

func (x FunctionedTupleToUserset_Function) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FunctionedTupleToUserset_Function) Descriptor() protoreflect.EnumDescriptor {
	return file_core_v1_core_proto_enumTypes[4].Descriptor()
}

func (FunctionedTupleToUserset_Function) Type() protoreflect.EnumType {
	return &file_core_v1_core_proto_enumTypes[4]
}

func (x FunctionedTupleToUserset_Function) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FunctionedTupleToUserset_Function.Descriptor instead.
func (FunctionedTupleToUserset_Function) EnumDescriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{26, 0}
}

type ComputedUserset_Object int32

const (
	ComputedUserset_TUPLE_OBJECT         ComputedUserset_Object = 0
	ComputedUserset_TUPLE_USERSET_OBJECT ComputedUserset_Object = 1
)

// Enum value maps for ComputedUserset_Object.
var (
	ComputedUserset_Object_name = map[int32]string{
		0: "TUPLE_OBJECT",
		1: "TUPLE_USERSET_OBJECT",
	}
	ComputedUserset_Object_value = map[string]int32{
		"TUPLE_OBJECT":         0,
		"TUPLE_USERSET_OBJECT": 1,
	}
)

func (x ComputedUserset_Object) Enum() *ComputedUserset_Object {
	p := new(ComputedUserset_Object)
	*p = x
	return p
}

func (x ComputedUserset_Object) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ComputedUserset_Object) Descriptor() protoreflect.EnumDescriptor {
	return file_core_v1_core_proto_enumTypes[5].Descriptor()
}

func (ComputedUserset_Object) Type() protoreflect.EnumType {
	return &file_core_v1_core_proto_enumTypes[5]
}

func (x ComputedUserset_Object) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ComputedUserset_Object.Descriptor instead.
func (ComputedUserset_Object) EnumDescriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{27, 0}
}

type CaveatOperation_Operation int32

const (
	CaveatOperation_UNKNOWN CaveatOperation_Operation = 0
	CaveatOperation_OR      CaveatOperation_Operation = 1
	CaveatOperation_AND     CaveatOperation_Operation = 2
	CaveatOperation_NOT     CaveatOperation_Operation = 3
)

// Enum value maps for CaveatOperation_Operation.
var (
	CaveatOperation_Operation_name = map[int32]string{
		0: "UNKNOWN",
		1: "OR",
		2: "AND",
		3: "NOT",
	}
	CaveatOperation_Operation_value = map[string]int32{
		"UNKNOWN": 0,
		"OR":      1,
		"AND":     2,
		"NOT":     3,
	}
)

func (x CaveatOperation_Operation) Enum() *CaveatOperation_Operation {
	p := new(CaveatOperation_Operation)
	*p = x
	return p
}

func (x CaveatOperation_Operation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CaveatOperation_Operation) Descriptor() protoreflect.EnumDescriptor {
	return file_core_v1_core_proto_enumTypes[6].Descriptor()
}

func (CaveatOperation_Operation) Type() protoreflect.EnumType {
	return &file_core_v1_core_proto_enumTypes[6]
}

func (x CaveatOperation_Operation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CaveatOperation_Operation.Descriptor instead.
func (CaveatOperation_Operation) EnumDescriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{30, 0}
}

type RelationTuple struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// * resource_and_relation is the resource for the tuple
	ResourceAndRelation *ObjectAndRelation `protobuf:"bytes,1,opt,name=resource_and_relation,json=resourceAndRelation,proto3" json:"resource_and_relation,omitempty"`
	// * subject is the subject for the tuple
	Subject *ObjectAndRelation `protobuf:"bytes,2,opt,name=subject,proto3" json:"subject,omitempty"`
	// * caveat is a reference to a the caveat that must be enforced over the tuple *
	Caveat *ContextualizedCaveat `protobuf:"bytes,3,opt,name=caveat,proto3" json:"caveat,omitempty"`
	// * integrity holds (optional) information about the integrity of the tuple
	Integrity *RelationshipIntegrity `protobuf:"bytes,4,opt,name=integrity,proto3" json:"integrity,omitempty"`
	// * optional_expiration_time is the (optional) time at which the tuple expires
	OptionalExpirationTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=optional_expiration_time,json=optionalExpirationTime,proto3" json:"optional_expiration_time,omitempty"`
}

func (x *RelationTuple) Reset() {
	*x = RelationTuple{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationTuple) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationTuple) ProtoMessage() {}

func (x *RelationTuple) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationTuple.ProtoReflect.Descriptor instead.
func (*RelationTuple) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{0}
}

func (x *RelationTuple) GetResourceAndRelation() *ObjectAndRelation {
	if x != nil {
		return x.ResourceAndRelation
	}
	return nil
}

func (x *RelationTuple) GetSubject() *ObjectAndRelation {
	if x != nil {
		return x.Subject
	}
	return nil
}

func (x *RelationTuple) GetCaveat() *ContextualizedCaveat {
	if x != nil {
		return x.Caveat
	}
	return nil
}

func (x *RelationTuple) GetIntegrity() *RelationshipIntegrity {
	if x != nil {
		return x.Integrity
	}
	return nil
}

func (x *RelationTuple) GetOptionalExpirationTime() *timestamppb.Timestamp {
	if x != nil {
		return x.OptionalExpirationTime
	}
	return nil
}

type RelationshipIntegrity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// * key_id is the key ID used to hash the tuple
	KeyId string `protobuf:"bytes,1,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	// * hash is the hash of the tuple
	Hash []byte `protobuf:"bytes,2,opt,name=hash,proto3" json:"hash,omitempty"`
	// * hashed_at is the timestamp when the tuple was hashed
	HashedAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=hashed_at,json=hashedAt,proto3" json:"hashed_at,omitempty"`
}

func (x *RelationshipIntegrity) Reset() {
	*x = RelationshipIntegrity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationshipIntegrity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationshipIntegrity) ProtoMessage() {}

func (x *RelationshipIntegrity) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationshipIntegrity.ProtoReflect.Descriptor instead.
func (*RelationshipIntegrity) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{1}
}

func (x *RelationshipIntegrity) GetKeyId() string {
	if x != nil {
		return x.KeyId
	}
	return ""
}

func (x *RelationshipIntegrity) GetHash() []byte {
	if x != nil {
		return x.Hash
	}
	return nil
}

func (x *RelationshipIntegrity) GetHashedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.HashedAt
	}
	return nil
}

// *
// ContextualizedCaveat represents a reference to a caveat used to by caveated tuples.
// The context are key-value pairs that will be injected at evaluation time.
type ContextualizedCaveat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// * caveat_name is the name used in the schema for a stored caveat *
	CaveatName string `protobuf:"bytes,1,opt,name=caveat_name,json=caveatName,proto3" json:"caveat_name,omitempty"`
	// * context are arguments used as input during caveat evaluation with a predefined value *
	Context *structpb.Struct `protobuf:"bytes,2,opt,name=context,proto3" json:"context,omitempty"`
}

func (x *ContextualizedCaveat) Reset() {
	*x = ContextualizedCaveat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContextualizedCaveat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContextualizedCaveat) ProtoMessage() {}

func (x *ContextualizedCaveat) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContextualizedCaveat.ProtoReflect.Descriptor instead.
func (*ContextualizedCaveat) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{2}
}

func (x *ContextualizedCaveat) GetCaveatName() string {
	if x != nil {
		return x.CaveatName
	}
	return ""
}

func (x *ContextualizedCaveat) GetContext() *structpb.Struct {
	if x != nil {
		return x.Context
	}
	return nil
}

type CaveatDefinition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// * name represents the globally-unique identifier of the caveat *
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// * serialized_expression is the byte representation of a caveat's logic
	SerializedExpression []byte `protobuf:"bytes,2,opt,name=serialized_expression,json=serializedExpression,proto3" json:"serialized_expression,omitempty"`
	// * parameters_and_types is a map from parameter name to its type
	ParameterTypes map[string]*CaveatTypeReference `protobuf:"bytes,3,rep,name=parameter_types,json=parameterTypes,proto3" json:"parameter_types,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// * metadata contains compiler metadata from schemas compiled into caveats
	Metadata *Metadata `protobuf:"bytes,4,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// * source_position contains the position of the caveat in the source schema, if any
	SourcePosition *SourcePosition `protobuf:"bytes,5,opt,name=source_position,json=sourcePosition,proto3" json:"source_position,omitempty"`
}

func (x *CaveatDefinition) Reset() {
	*x = CaveatDefinition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaveatDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaveatDefinition) ProtoMessage() {}

func (x *CaveatDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaveatDefinition.ProtoReflect.Descriptor instead.
func (*CaveatDefinition) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{3}
}

func (x *CaveatDefinition) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CaveatDefinition) GetSerializedExpression() []byte {
	if x != nil {
		return x.SerializedExpression
	}
	return nil
}

func (x *CaveatDefinition) GetParameterTypes() map[string]*CaveatTypeReference {
	if x != nil {
		return x.ParameterTypes
	}
	return nil
}

func (x *CaveatDefinition) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CaveatDefinition) GetSourcePosition() *SourcePosition {
	if x != nil {
		return x.SourcePosition
	}
	return nil
}

type CaveatTypeReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TypeName   string                 `protobuf:"bytes,1,opt,name=type_name,json=typeName,proto3" json:"type_name,omitempty"`
	ChildTypes []*CaveatTypeReference `protobuf:"bytes,2,rep,name=child_types,json=childTypes,proto3" json:"child_types,omitempty"`
}

func (x *CaveatTypeReference) Reset() {
	*x = CaveatTypeReference{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaveatTypeReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaveatTypeReference) ProtoMessage() {}

func (x *CaveatTypeReference) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaveatTypeReference.ProtoReflect.Descriptor instead.
func (*CaveatTypeReference) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{4}
}

func (x *CaveatTypeReference) GetTypeName() string {
	if x != nil {
		return x.TypeName
	}
	return ""
}

func (x *CaveatTypeReference) GetChildTypes() []*CaveatTypeReference {
	if x != nil {
		return x.ChildTypes
	}
	return nil
}

type ObjectAndRelation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// * namespace is the full namespace path for the referenced object
	Namespace string `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// * object_id is the unique ID for the object within the namespace
	ObjectId string `protobuf:"bytes,2,opt,name=object_id,json=objectId,proto3" json:"object_id,omitempty"`
	// * relation is the name of the referenced relation or permission under the namespace
	Relation string `protobuf:"bytes,3,opt,name=relation,proto3" json:"relation,omitempty"`
}

func (x *ObjectAndRelation) Reset() {
	*x = ObjectAndRelation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ObjectAndRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObjectAndRelation) ProtoMessage() {}

func (x *ObjectAndRelation) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObjectAndRelation.ProtoReflect.Descriptor instead.
func (*ObjectAndRelation) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{5}
}

func (x *ObjectAndRelation) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ObjectAndRelation) GetObjectId() string {
	if x != nil {
		return x.ObjectId
	}
	return ""
}

func (x *ObjectAndRelation) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

type RelationReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// * namespace is the full namespace path
	Namespace string `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// * relation is the name of the referenced relation or permission under the namespace
	Relation string `protobuf:"bytes,3,opt,name=relation,proto3" json:"relation,omitempty"`
}

func (x *RelationReference) Reset() {
	*x = RelationReference{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationReference) ProtoMessage() {}

func (x *RelationReference) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationReference.ProtoReflect.Descriptor instead.
func (*RelationReference) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{6}
}

func (x *RelationReference) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *RelationReference) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

type Zookie struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *Zookie) Reset() {
	*x = Zookie{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Zookie) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Zookie) ProtoMessage() {}

func (x *Zookie) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Zookie.ProtoReflect.Descriptor instead.
func (*Zookie) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{7}
}

func (x *Zookie) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type RelationTupleUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Operation RelationTupleUpdate_Operation `protobuf:"varint,1,opt,name=operation,proto3,enum=core.v1.RelationTupleUpdate_Operation" json:"operation,omitempty"`
	Tuple     *RelationTuple                `protobuf:"bytes,2,opt,name=tuple,proto3" json:"tuple,omitempty"`
}

func (x *RelationTupleUpdate) Reset() {
	*x = RelationTupleUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationTupleUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationTupleUpdate) ProtoMessage() {}

func (x *RelationTupleUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationTupleUpdate.ProtoReflect.Descriptor instead.
func (*RelationTupleUpdate) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{8}
}

func (x *RelationTupleUpdate) GetOperation() RelationTupleUpdate_Operation {
	if x != nil {
		return x.Operation
	}
	return RelationTupleUpdate_UNKNOWN
}

func (x *RelationTupleUpdate) GetTuple() *RelationTuple {
	if x != nil {
		return x.Tuple
	}
	return nil
}

type RelationTupleTreeNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to NodeType:
	//
	//	*RelationTupleTreeNode_IntermediateNode
	//	*RelationTupleTreeNode_LeafNode
	NodeType         isRelationTupleTreeNode_NodeType `protobuf_oneof:"node_type"`
	Expanded         *ObjectAndRelation               `protobuf:"bytes,3,opt,name=expanded,proto3" json:"expanded,omitempty"`
	CaveatExpression *CaveatExpression                `protobuf:"bytes,4,opt,name=caveat_expression,json=caveatExpression,proto3" json:"caveat_expression,omitempty"`
}

func (x *RelationTupleTreeNode) Reset() {
	*x = RelationTupleTreeNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationTupleTreeNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationTupleTreeNode) ProtoMessage() {}

func (x *RelationTupleTreeNode) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationTupleTreeNode.ProtoReflect.Descriptor instead.
func (*RelationTupleTreeNode) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{9}
}

func (m *RelationTupleTreeNode) GetNodeType() isRelationTupleTreeNode_NodeType {
	if m != nil {
		return m.NodeType
	}
	return nil
}

func (x *RelationTupleTreeNode) GetIntermediateNode() *SetOperationUserset {
	if x, ok := x.GetNodeType().(*RelationTupleTreeNode_IntermediateNode); ok {
		return x.IntermediateNode
	}
	return nil
}

func (x *RelationTupleTreeNode) GetLeafNode() *DirectSubjects {
	if x, ok := x.GetNodeType().(*RelationTupleTreeNode_LeafNode); ok {
		return x.LeafNode
	}
	return nil
}

func (x *RelationTupleTreeNode) GetExpanded() *ObjectAndRelation {
	if x != nil {
		return x.Expanded
	}
	return nil
}

func (x *RelationTupleTreeNode) GetCaveatExpression() *CaveatExpression {
	if x != nil {
		return x.CaveatExpression
	}
	return nil
}

type isRelationTupleTreeNode_NodeType interface {
	isRelationTupleTreeNode_NodeType()
}

type RelationTupleTreeNode_IntermediateNode struct {
	IntermediateNode *SetOperationUserset `protobuf:"bytes,1,opt,name=intermediate_node,json=intermediateNode,proto3,oneof"`
}

type RelationTupleTreeNode_LeafNode struct {
	LeafNode *DirectSubjects `protobuf:"bytes,2,opt,name=leaf_node,json=leafNode,proto3,oneof"`
}

func (*RelationTupleTreeNode_IntermediateNode) isRelationTupleTreeNode_NodeType() {}

func (*RelationTupleTreeNode_LeafNode) isRelationTupleTreeNode_NodeType() {}

type SetOperationUserset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Operation  SetOperationUserset_Operation `protobuf:"varint,1,opt,name=operation,proto3,enum=core.v1.SetOperationUserset_Operation" json:"operation,omitempty"`
	ChildNodes []*RelationTupleTreeNode      `protobuf:"bytes,2,rep,name=child_nodes,json=childNodes,proto3" json:"child_nodes,omitempty"`
}

func (x *SetOperationUserset) Reset() {
	*x = SetOperationUserset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetOperationUserset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetOperationUserset) ProtoMessage() {}

func (x *SetOperationUserset) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetOperationUserset.ProtoReflect.Descriptor instead.
func (*SetOperationUserset) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{10}
}

func (x *SetOperationUserset) GetOperation() SetOperationUserset_Operation {
	if x != nil {
		return x.Operation
	}
	return SetOperationUserset_INVALID
}

func (x *SetOperationUserset) GetChildNodes() []*RelationTupleTreeNode {
	if x != nil {
		return x.ChildNodes
	}
	return nil
}

type DirectSubject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject          *ObjectAndRelation `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`
	CaveatExpression *CaveatExpression  `protobuf:"bytes,2,opt,name=caveat_expression,json=caveatExpression,proto3" json:"caveat_expression,omitempty"`
}

func (x *DirectSubject) Reset() {
	*x = DirectSubject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectSubject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectSubject) ProtoMessage() {}

func (x *DirectSubject) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectSubject.ProtoReflect.Descriptor instead.
func (*DirectSubject) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{11}
}

func (x *DirectSubject) GetSubject() *ObjectAndRelation {
	if x != nil {
		return x.Subject
	}
	return nil
}

func (x *DirectSubject) GetCaveatExpression() *CaveatExpression {
	if x != nil {
		return x.CaveatExpression
	}
	return nil
}

type DirectSubjects struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subjects []*DirectSubject `protobuf:"bytes,1,rep,name=subjects,proto3" json:"subjects,omitempty"`
}

func (x *DirectSubjects) Reset() {
	*x = DirectSubjects{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectSubjects) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectSubjects) ProtoMessage() {}

func (x *DirectSubjects) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectSubjects.ProtoReflect.Descriptor instead.
func (*DirectSubjects) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{12}
}

func (x *DirectSubjects) GetSubjects() []*DirectSubject {
	if x != nil {
		return x.Subjects
	}
	return nil
}

// *
// Metadata is compiler metadata added to namespace definitions, such as doc comments and
// relation kinds.
type Metadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MetadataMessage []*anypb.Any `protobuf:"bytes,1,rep,name=metadata_message,json=metadataMessage,proto3" json:"metadata_message,omitempty"`
}

func (x *Metadata) Reset() {
	*x = Metadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metadata) ProtoMessage() {}

func (x *Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metadata.ProtoReflect.Descriptor instead.
func (*Metadata) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{13}
}

func (x *Metadata) GetMetadataMessage() []*anypb.Any {
	if x != nil {
		return x.MetadataMessage
	}
	return nil
}

// *
// NamespaceDefinition represents a single definition of an object type
type NamespaceDefinition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// * name is the unique for the namespace, including prefixes (which are optional)
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// * relation contains the relations and permissions defined in the namespace
	Relation []*Relation `protobuf:"bytes,2,rep,name=relation,proto3" json:"relation,omitempty"`
	// * metadata contains compiler metadata from schemas compiled into namespaces
	Metadata *Metadata `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// * source_position contains the position of the namespace in the source schema, if any
	SourcePosition *SourcePosition `protobuf:"bytes,4,opt,name=source_position,json=sourcePosition,proto3" json:"source_position,omitempty"`
}

func (x *NamespaceDefinition) Reset() {
	*x = NamespaceDefinition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceDefinition) ProtoMessage() {}

func (x *NamespaceDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceDefinition.ProtoReflect.Descriptor instead.
func (*NamespaceDefinition) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{14}
}

func (x *NamespaceDefinition) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamespaceDefinition) GetRelation() []*Relation {
	if x != nil {
		return x.Relation
	}
	return nil
}

func (x *NamespaceDefinition) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *NamespaceDefinition) GetSourcePosition() *SourcePosition {
	if x != nil {
		return x.SourcePosition
	}
	return nil
}

// *
// Relation represents the definition of a relation or permission under a namespace.
type Relation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// * name is the full name for the relation or permission
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// * userset_rewrite, if specified, is the rewrite for computing the value of the permission.
	UsersetRewrite *UsersetRewrite `protobuf:"bytes,2,opt,name=userset_rewrite,json=usersetRewrite,proto3" json:"userset_rewrite,omitempty"`
	// *
	// type_information, if specified, is the list of allowed object types that can appear in this
	// relation
	TypeInformation *TypeInformation `protobuf:"bytes,3,opt,name=type_information,json=typeInformation,proto3" json:"type_information,omitempty"`
	// * metadata contains compiler metadata from schemas compiled into namespaces
	Metadata *Metadata `protobuf:"bytes,4,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// * source_position contains the position of the relation in the source schema, if any
	SourcePosition    *SourcePosition `protobuf:"bytes,5,opt,name=source_position,json=sourcePosition,proto3" json:"source_position,omitempty"`
	AliasingRelation  string          `protobuf:"bytes,6,opt,name=aliasing_relation,json=aliasingRelation,proto3" json:"aliasing_relation,omitempty"`
	CanonicalCacheKey string          `protobuf:"bytes,7,opt,name=canonical_cache_key,json=canonicalCacheKey,proto3" json:"canonical_cache_key,omitempty"`
}

func (x *Relation) Reset() {
	*x = Relation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Relation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Relation) ProtoMessage() {}

func (x *Relation) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Relation.ProtoReflect.Descriptor instead.
func (*Relation) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{15}
}

func (x *Relation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Relation) GetUsersetRewrite() *UsersetRewrite {
	if x != nil {
		return x.UsersetRewrite
	}
	return nil
}

func (x *Relation) GetTypeInformation() *TypeInformation {
	if x != nil {
		return x.TypeInformation
	}
	return nil
}

func (x *Relation) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Relation) GetSourcePosition() *SourcePosition {
	if x != nil {
		return x.SourcePosition
	}
	return nil
}

func (x *Relation) GetAliasingRelation() string {
	if x != nil {
		return x.AliasingRelation
	}
	return ""
}

func (x *Relation) GetCanonicalCacheKey() string {
	if x != nil {
		return x.CanonicalCacheKey
	}
	return ""
}

// *
// ReachabilityGraph is a serialized form of a reachability graph, representing how a relation can
// be reached from one or more subject types.
//
// It defines a "reverse" data flow graph, starting at a subject type, and providing all the
// entrypoints where that subject type can be found leading to the decorated relation.
//
// For example, given the schema:
// ```
//
//	definition user {}
//
//	definition organization {
//	  relation admin: user
//	}
//
//	definition resource {
//	  relation org: organization
//	  relation viewer: user
//	  relation owner: user
//	  permission view = viewer + owner + org->admin
//	}
//
// ```
//
// The reachability graph for `viewer` and the other relations will have entrypoints for each
// subject type found for those relations.
//
// The full reachability graph for the `view` relation will have three entrypoints, representing:
//  1. resource#viewer (computed_userset)
//  2. resource#owner  (computed_userset)
//  3. organization#admin (tupleset_to_userset)
type ReachabilityGraph struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// *
	// entrypoints_by_subject_type provides all entrypoints by subject *type*, representing wildcards.
	// The keys of the map are the full path(s) for the namespace(s) referenced by reachable wildcards
	EntrypointsBySubjectType map[string]*ReachabilityEntrypoints `protobuf:"bytes,1,rep,name=entrypoints_by_subject_type,json=entrypointsBySubjectType,proto3" json:"entrypoints_by_subject_type,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// *
	// entrypoints_by_subject_relation provides all entrypoints by subject type+relation.
	// The keys of the map are of the form `namespace_path#relation_name`
	EntrypointsBySubjectRelation map[string]*ReachabilityEntrypoints `protobuf:"bytes,2,rep,name=entrypoints_by_subject_relation,json=entrypointsBySubjectRelation,proto3" json:"entrypoints_by_subject_relation,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ReachabilityGraph) Reset() {
	*x = ReachabilityGraph{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReachabilityGraph) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReachabilityGraph) ProtoMessage() {}

func (x *ReachabilityGraph) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReachabilityGraph.ProtoReflect.Descriptor instead.
func (*ReachabilityGraph) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{16}
}

func (x *ReachabilityGraph) GetEntrypointsBySubjectType() map[string]*ReachabilityEntrypoints {
	if x != nil {
		return x.EntrypointsBySubjectType
	}
	return nil
}

func (x *ReachabilityGraph) GetEntrypointsBySubjectRelation() map[string]*ReachabilityEntrypoints {
	if x != nil {
		return x.EntrypointsBySubjectRelation
	}
	return nil
}

// *
// ReachabilityEntrypoints represents all the entrypoints for a specific subject type or subject
// relation into the reachability graph for a particular target relation.
type ReachabilityEntrypoints struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// *
	// entrypoints are the entrypoints found.
	Entrypoints []*ReachabilityEntrypoint `protobuf:"bytes,1,rep,name=entrypoints,proto3" json:"entrypoints,omitempty"`
	// *
	// subject_type, if specified, is the type of subjects to which the entrypoint(s) apply. A
	// subject type is only set for wildcards.
	SubjectType string `protobuf:"bytes,2,opt,name=subject_type,json=subjectType,proto3" json:"subject_type,omitempty"`
	// *
	// subject_relation, if specified, is the type and relation of subjects to which the
	// entrypoint(s) apply.
	SubjectRelation *RelationReference `protobuf:"bytes,3,opt,name=subject_relation,json=subjectRelation,proto3" json:"subject_relation,omitempty"`
}

func (x *ReachabilityEntrypoints) Reset() {
	*x = ReachabilityEntrypoints{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReachabilityEntrypoints) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReachabilityEntrypoints) ProtoMessage() {}

func (x *ReachabilityEntrypoints) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReachabilityEntrypoints.ProtoReflect.Descriptor instead.
func (*ReachabilityEntrypoints) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{17}
}

func (x *ReachabilityEntrypoints) GetEntrypoints() []*ReachabilityEntrypoint {
	if x != nil {
		return x.Entrypoints
	}
	return nil
}

func (x *ReachabilityEntrypoints) GetSubjectType() string {
	if x != nil {
		return x.SubjectType
	}
	return ""
}

func (x *ReachabilityEntrypoints) GetSubjectRelation() *RelationReference {
	if x != nil {
		return x.SubjectRelation
	}
	return nil
}

// *
// ReachabilityEntrypoint represents a single entrypoint for a specific subject type or subject
// relation into the reachability graph for a particular target relation.
type ReachabilityEntrypoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// *
	// kind is the kind of the entrypoint.
	Kind ReachabilityEntrypoint_ReachabilityEntrypointKind `protobuf:"varint,1,opt,name=kind,proto3,enum=core.v1.ReachabilityEntrypoint_ReachabilityEntrypointKind" json:"kind,omitempty"`
	// *
	// target_relation is the relation on which the entrypoint exists.
	TargetRelation *RelationReference `protobuf:"bytes,2,opt,name=target_relation,json=targetRelation,proto3" json:"target_relation,omitempty"`
	// *
	// result_status contains the status of objects found for this entrypoint as direct results for
	// the parent relation/permission.
	ResultStatus ReachabilityEntrypoint_EntrypointResultStatus `protobuf:"varint,4,opt,name=result_status,json=resultStatus,proto3,enum=core.v1.ReachabilityEntrypoint_EntrypointResultStatus" json:"result_status,omitempty"`
	// *
	// tupleset_relation is the name of the tupleset relation on the TupleToUserset this entrypoint
	// represents, if applicable.
	TuplesetRelation string `protobuf:"bytes,5,opt,name=tupleset_relation,json=tuplesetRelation,proto3" json:"tupleset_relation,omitempty"`
	// *
	// computed_userset_relation is the name of the computed userset relation on the ComputedUserset
	// this entrypoint represents, if applicable.
	ComputedUsersetRelation string `protobuf:"bytes,6,opt,name=computed_userset_relation,json=computedUsersetRelation,proto3" json:"computed_userset_relation,omitempty"`
}

func (x *ReachabilityEntrypoint) Reset() {
	*x = ReachabilityEntrypoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReachabilityEntrypoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReachabilityEntrypoint) ProtoMessage() {}

func (x *ReachabilityEntrypoint) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReachabilityEntrypoint.ProtoReflect.Descriptor instead.
func (*ReachabilityEntrypoint) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{18}
}

func (x *ReachabilityEntrypoint) GetKind() ReachabilityEntrypoint_ReachabilityEntrypointKind {
	if x != nil {
		return x.Kind
	}
	return ReachabilityEntrypoint_RELATION_ENTRYPOINT
}

func (x *ReachabilityEntrypoint) GetTargetRelation() *RelationReference {
	if x != nil {
		return x.TargetRelation
	}
	return nil
}

func (x *ReachabilityEntrypoint) GetResultStatus() ReachabilityEntrypoint_EntrypointResultStatus {
	if x != nil {
		return x.ResultStatus
	}
	return ReachabilityEntrypoint_REACHABLE_CONDITIONAL_RESULT
}

func (x *ReachabilityEntrypoint) GetTuplesetRelation() string {
	if x != nil {
		return x.TuplesetRelation
	}
	return ""
}

func (x *ReachabilityEntrypoint) GetComputedUsersetRelation() string {
	if x != nil {
		return x.ComputedUsersetRelation
	}
	return ""
}

// *
// TypeInformation defines the allowed types for a relation.
type TypeInformation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// *
	// allowed_direct_relations are those relation types allowed to be placed into a relation,
	// e.g. the types of subjects allowed when a relationship is written to the relation
	AllowedDirectRelations []*AllowedRelation `protobuf:"bytes,1,rep,name=allowed_direct_relations,json=allowedDirectRelations,proto3" json:"allowed_direct_relations,omitempty"`
}

func (x *TypeInformation) Reset() {
	*x = TypeInformation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TypeInformation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TypeInformation) ProtoMessage() {}

func (x *TypeInformation) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TypeInformation.ProtoReflect.Descriptor instead.
func (*TypeInformation) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{19}
}

func (x *TypeInformation) GetAllowedDirectRelations() []*AllowedRelation {
	if x != nil {
		return x.AllowedDirectRelations
	}
	return nil
}

// *
// AllowedRelation is an allowed type of a relation when used as a subject.
type AllowedRelation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// * namespace is the full namespace path of the allowed object type
	Namespace string `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// *
	// relation_or_wildcard indicates the relation for the objects, or a wildcard.
	//
	// Types that are assignable to RelationOrWildcard:
	//
	//	*AllowedRelation_Relation
	//	*AllowedRelation_PublicWildcard_
	RelationOrWildcard isAllowedRelation_RelationOrWildcard `protobuf_oneof:"relation_or_wildcard"`
	// * source_position contains the position of the type in the source schema, if any
	SourcePosition *SourcePosition `protobuf:"bytes,5,opt,name=source_position,json=sourcePosition,proto3" json:"source_position,omitempty"`
	// *
	// required_caveat defines the required caveat on this relation.
	RequiredCaveat *AllowedCaveat `protobuf:"bytes,6,opt,name=required_caveat,json=requiredCaveat,proto3" json:"required_caveat,omitempty"`
	// *
	// required_expiration defines the required expiration on this relation.
	RequiredExpiration *ExpirationTrait `protobuf:"bytes,7,opt,name=required_expiration,json=requiredExpiration,proto3" json:"required_expiration,omitempty"`
}

func (x *AllowedRelation) Reset() {
	*x = AllowedRelation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllowedRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllowedRelation) ProtoMessage() {}

func (x *AllowedRelation) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllowedRelation.ProtoReflect.Descriptor instead.
func (*AllowedRelation) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{20}
}

func (x *AllowedRelation) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (m *AllowedRelation) GetRelationOrWildcard() isAllowedRelation_RelationOrWildcard {
	if m != nil {
		return m.RelationOrWildcard
	}
	return nil
}

func (x *AllowedRelation) GetRelation() string {
	if x, ok := x.GetRelationOrWildcard().(*AllowedRelation_Relation); ok {
		return x.Relation
	}
	return ""
}

func (x *AllowedRelation) GetPublicWildcard() *AllowedRelation_PublicWildcard {
	if x, ok := x.GetRelationOrWildcard().(*AllowedRelation_PublicWildcard_); ok {
		return x.PublicWildcard
	}
	return nil
}

func (x *AllowedRelation) GetSourcePosition() *SourcePosition {
	if x != nil {
		return x.SourcePosition
	}
	return nil
}

func (x *AllowedRelation) GetRequiredCaveat() *AllowedCaveat {
	if x != nil {
		return x.RequiredCaveat
	}
	return nil
}

func (x *AllowedRelation) GetRequiredExpiration() *ExpirationTrait {
	if x != nil {
		return x.RequiredExpiration
	}
	return nil
}

type isAllowedRelation_RelationOrWildcard interface {
	isAllowedRelation_RelationOrWildcard()
}

type AllowedRelation_Relation struct {
	Relation string `protobuf:"bytes,3,opt,name=relation,proto3,oneof"`
}

type AllowedRelation_PublicWildcard_ struct {
	PublicWildcard *AllowedRelation_PublicWildcard `protobuf:"bytes,4,opt,name=public_wildcard,json=publicWildcard,proto3,oneof"`
}

func (*AllowedRelation_Relation) isAllowedRelation_RelationOrWildcard() {}

func (*AllowedRelation_PublicWildcard_) isAllowedRelation_RelationOrWildcard() {}

// *
// ExpirationTrait is an expiration trait of a relation.
type ExpirationTrait struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ExpirationTrait) Reset() {
	*x = ExpirationTrait{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpirationTrait) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpirationTrait) ProtoMessage() {}

func (x *ExpirationTrait) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpirationTrait.ProtoReflect.Descriptor instead.
func (*ExpirationTrait) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{21}
}

// *
// AllowedCaveat is an allowed caveat of a relation.
type AllowedCaveat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// *
	// caveat_name is the name of the allowed caveat.
	CaveatName string `protobuf:"bytes,1,opt,name=caveat_name,json=caveatName,proto3" json:"caveat_name,omitempty"`
}

func (x *AllowedCaveat) Reset() {
	*x = AllowedCaveat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllowedCaveat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllowedCaveat) ProtoMessage() {}

func (x *AllowedCaveat) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllowedCaveat.ProtoReflect.Descriptor instead.
func (*AllowedCaveat) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{22}
}

func (x *AllowedCaveat) GetCaveatName() string {
	if x != nil {
		return x.CaveatName
	}
	return ""
}

type UsersetRewrite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to RewriteOperation:
	//
	//	*UsersetRewrite_Union
	//	*UsersetRewrite_Intersection
	//	*UsersetRewrite_Exclusion
	RewriteOperation isUsersetRewrite_RewriteOperation `protobuf_oneof:"rewrite_operation"`
	SourcePosition   *SourcePosition                   `protobuf:"bytes,4,opt,name=source_position,json=sourcePosition,proto3" json:"source_position,omitempty"`
}

func (x *UsersetRewrite) Reset() {
	*x = UsersetRewrite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsersetRewrite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersetRewrite) ProtoMessage() {}

func (x *UsersetRewrite) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersetRewrite.ProtoReflect.Descriptor instead.
func (*UsersetRewrite) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{23}
}

func (m *UsersetRewrite) GetRewriteOperation() isUsersetRewrite_RewriteOperation {
	if m != nil {
		return m.RewriteOperation
	}
	return nil
}

func (x *UsersetRewrite) GetUnion() *SetOperation {
	if x, ok := x.GetRewriteOperation().(*UsersetRewrite_Union); ok {
		return x.Union
	}
	return nil
}

func (x *UsersetRewrite) GetIntersection() *SetOperation {
	if x, ok := x.GetRewriteOperation().(*UsersetRewrite_Intersection); ok {
		return x.Intersection
	}
	return nil
}

func (x *UsersetRewrite) GetExclusion() *SetOperation {
	if x, ok := x.GetRewriteOperation().(*UsersetRewrite_Exclusion); ok {
		return x.Exclusion
	}
	return nil
}

func (x *UsersetRewrite) GetSourcePosition() *SourcePosition {
	if x != nil {
		return x.SourcePosition
	}
	return nil
}

type isUsersetRewrite_RewriteOperation interface {
	isUsersetRewrite_RewriteOperation()
}

type UsersetRewrite_Union struct {
	Union *SetOperation `protobuf:"bytes,1,opt,name=union,proto3,oneof"`
}

type UsersetRewrite_Intersection struct {
	Intersection *SetOperation `protobuf:"bytes,2,opt,name=intersection,proto3,oneof"`
}

type UsersetRewrite_Exclusion struct {
	Exclusion *SetOperation `protobuf:"bytes,3,opt,name=exclusion,proto3,oneof"`
}

func (*UsersetRewrite_Union) isUsersetRewrite_RewriteOperation() {}

func (*UsersetRewrite_Intersection) isUsersetRewrite_RewriteOperation() {}

func (*UsersetRewrite_Exclusion) isUsersetRewrite_RewriteOperation() {}

type SetOperation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Child []*SetOperation_Child `protobuf:"bytes,1,rep,name=child,proto3" json:"child,omitempty"`
}

func (x *SetOperation) Reset() {
	*x = SetOperation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetOperation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetOperation) ProtoMessage() {}

func (x *SetOperation) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetOperation.ProtoReflect.Descriptor instead.
func (*SetOperation) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{24}
}

func (x *SetOperation) GetChild() []*SetOperation_Child {
	if x != nil {
		return x.Child
	}
	return nil
}

type TupleToUserset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tupleset        *TupleToUserset_Tupleset `protobuf:"bytes,1,opt,name=tupleset,proto3" json:"tupleset,omitempty"`
	ComputedUserset *ComputedUserset         `protobuf:"bytes,2,opt,name=computed_userset,json=computedUserset,proto3" json:"computed_userset,omitempty"`
	SourcePosition  *SourcePosition          `protobuf:"bytes,3,opt,name=source_position,json=sourcePosition,proto3" json:"source_position,omitempty"`
}

func (x *TupleToUserset) Reset() {
	*x = TupleToUserset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TupleToUserset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TupleToUserset) ProtoMessage() {}

func (x *TupleToUserset) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TupleToUserset.ProtoReflect.Descriptor instead.
func (*TupleToUserset) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{25}
}

func (x *TupleToUserset) GetTupleset() *TupleToUserset_Tupleset {
	if x != nil {
		return x.Tupleset
	}
	return nil
}

func (x *TupleToUserset) GetComputedUserset() *ComputedUserset {
	if x != nil {
		return x.ComputedUserset
	}
	return nil
}

func (x *TupleToUserset) GetSourcePosition() *SourcePosition {
	if x != nil {
		return x.SourcePosition
	}
	return nil
}

type FunctionedTupleToUserset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Function        FunctionedTupleToUserset_Function  `protobuf:"varint,1,opt,name=function,proto3,enum=core.v1.FunctionedTupleToUserset_Function" json:"function,omitempty"`
	Tupleset        *FunctionedTupleToUserset_Tupleset `protobuf:"bytes,2,opt,name=tupleset,proto3" json:"tupleset,omitempty"`
	ComputedUserset *ComputedUserset                   `protobuf:"bytes,3,opt,name=computed_userset,json=computedUserset,proto3" json:"computed_userset,omitempty"`
	SourcePosition  *SourcePosition                    `protobuf:"bytes,4,opt,name=source_position,json=sourcePosition,proto3" json:"source_position,omitempty"`
}

func (x *FunctionedTupleToUserset) Reset() {
	*x = FunctionedTupleToUserset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FunctionedTupleToUserset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunctionedTupleToUserset) ProtoMessage() {}

func (x *FunctionedTupleToUserset) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunctionedTupleToUserset.ProtoReflect.Descriptor instead.
func (*FunctionedTupleToUserset) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{26}
}

func (x *FunctionedTupleToUserset) GetFunction() FunctionedTupleToUserset_Function {
	if x != nil {
		return x.Function
	}
	return FunctionedTupleToUserset_FUNCTION_UNSPECIFIED
}

func (x *FunctionedTupleToUserset) GetTupleset() *FunctionedTupleToUserset_Tupleset {
	if x != nil {
		return x.Tupleset
	}
	return nil
}

func (x *FunctionedTupleToUserset) GetComputedUserset() *ComputedUserset {
	if x != nil {
		return x.ComputedUserset
	}
	return nil
}

func (x *FunctionedTupleToUserset) GetSourcePosition() *SourcePosition {
	if x != nil {
		return x.SourcePosition
	}
	return nil
}

type ComputedUserset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Object         ComputedUserset_Object `protobuf:"varint,1,opt,name=object,proto3,enum=core.v1.ComputedUserset_Object" json:"object,omitempty"`
	Relation       string                 `protobuf:"bytes,2,opt,name=relation,proto3" json:"relation,omitempty"`
	SourcePosition *SourcePosition        `protobuf:"bytes,3,opt,name=source_position,json=sourcePosition,proto3" json:"source_position,omitempty"`
}

func (x *ComputedUserset) Reset() {
	*x = ComputedUserset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComputedUserset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComputedUserset) ProtoMessage() {}

func (x *ComputedUserset) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComputedUserset.ProtoReflect.Descriptor instead.
func (*ComputedUserset) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{27}
}

func (x *ComputedUserset) GetObject() ComputedUserset_Object {
	if x != nil {
		return x.Object
	}
	return ComputedUserset_TUPLE_OBJECT
}

func (x *ComputedUserset) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

func (x *ComputedUserset) GetSourcePosition() *SourcePosition {
	if x != nil {
		return x.SourcePosition
	}
	return nil
}

type SourcePosition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZeroIndexedLineNumber     uint64 `protobuf:"varint,1,opt,name=zero_indexed_line_number,json=zeroIndexedLineNumber,proto3" json:"zero_indexed_line_number,omitempty"`
	ZeroIndexedColumnPosition uint64 `protobuf:"varint,2,opt,name=zero_indexed_column_position,json=zeroIndexedColumnPosition,proto3" json:"zero_indexed_column_position,omitempty"`
}

func (x *SourcePosition) Reset() {
	*x = SourcePosition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourcePosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourcePosition) ProtoMessage() {}

func (x *SourcePosition) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourcePosition.ProtoReflect.Descriptor instead.
func (*SourcePosition) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{28}
}

func (x *SourcePosition) GetZeroIndexedLineNumber() uint64 {
	if x != nil {
		return x.ZeroIndexedLineNumber
	}
	return 0
}

func (x *SourcePosition) GetZeroIndexedColumnPosition() uint64 {
	if x != nil {
		return x.ZeroIndexedColumnPosition
	}
	return 0
}

type CaveatExpression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to OperationOrCaveat:
	//
	//	*CaveatExpression_Operation
	//	*CaveatExpression_Caveat
	OperationOrCaveat isCaveatExpression_OperationOrCaveat `protobuf_oneof:"operation_or_caveat"`
}

func (x *CaveatExpression) Reset() {
	*x = CaveatExpression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaveatExpression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaveatExpression) ProtoMessage() {}

func (x *CaveatExpression) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaveatExpression.ProtoReflect.Descriptor instead.
func (*CaveatExpression) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{29}
}

func (m *CaveatExpression) GetOperationOrCaveat() isCaveatExpression_OperationOrCaveat {
	if m != nil {
		return m.OperationOrCaveat
	}
	return nil
}

func (x *CaveatExpression) GetOperation() *CaveatOperation {
	if x, ok := x.GetOperationOrCaveat().(*CaveatExpression_Operation); ok {
		return x.Operation
	}
	return nil
}

func (x *CaveatExpression) GetCaveat() *ContextualizedCaveat {
	if x, ok := x.GetOperationOrCaveat().(*CaveatExpression_Caveat); ok {
		return x.Caveat
	}
	return nil
}

type isCaveatExpression_OperationOrCaveat interface {
	isCaveatExpression_OperationOrCaveat()
}

type CaveatExpression_Operation struct {
	Operation *CaveatOperation `protobuf:"bytes,1,opt,name=operation,proto3,oneof"`
}

type CaveatExpression_Caveat struct {
	Caveat *ContextualizedCaveat `protobuf:"bytes,2,opt,name=caveat,proto3,oneof"`
}

func (*CaveatExpression_Operation) isCaveatExpression_OperationOrCaveat() {}

func (*CaveatExpression_Caveat) isCaveatExpression_OperationOrCaveat() {}

type CaveatOperation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Op       CaveatOperation_Operation `protobuf:"varint,1,opt,name=op,proto3,enum=core.v1.CaveatOperation_Operation" json:"op,omitempty"`
	Children []*CaveatExpression       `protobuf:"bytes,2,rep,name=children,proto3" json:"children,omitempty"`
}

func (x *CaveatOperation) Reset() {
	*x = CaveatOperation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaveatOperation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaveatOperation) ProtoMessage() {}

func (x *CaveatOperation) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaveatOperation.ProtoReflect.Descriptor instead.
func (*CaveatOperation) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{30}
}

func (x *CaveatOperation) GetOp() CaveatOperation_Operation {
	if x != nil {
		return x.Op
	}
	return CaveatOperation_UNKNOWN
}

func (x *CaveatOperation) GetChildren() []*CaveatExpression {
	if x != nil {
		return x.Children
	}
	return nil
}

type RelationshipFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// resource_type is the *optional* resource type of the relationship.
	// NOTE: It is not prefixed with "optional_" for legacy compatibility.
	ResourceType string `protobuf:"bytes,1,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// optional_resource_id is the *optional* resource ID of the relationship.
	// If specified, optional_resource_id_prefix cannot be specified.
	OptionalResourceId string `protobuf:"bytes,2,opt,name=optional_resource_id,json=optionalResourceId,proto3" json:"optional_resource_id,omitempty"`
	// optional_resource_id_prefix is the *optional* prefix for the resource ID of the relationship.
	// If specified, optional_resource_id cannot be specified.
	OptionalResourceIdPrefix string `protobuf:"bytes,5,opt,name=optional_resource_id_prefix,json=optionalResourceIdPrefix,proto3" json:"optional_resource_id_prefix,omitempty"`
	// relation is the *optional* relation of the relationship.
	OptionalRelation string `protobuf:"bytes,3,opt,name=optional_relation,json=optionalRelation,proto3" json:"optional_relation,omitempty"`
	// optional_subject_filter is the optional filter for the subjects of the relationships.
	OptionalSubjectFilter *SubjectFilter `protobuf:"bytes,4,opt,name=optional_subject_filter,json=optionalSubjectFilter,proto3" json:"optional_subject_filter,omitempty"`
}

func (x *RelationshipFilter) Reset() {
	*x = RelationshipFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationshipFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationshipFilter) ProtoMessage() {}

func (x *RelationshipFilter) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationshipFilter.ProtoReflect.Descriptor instead.
func (*RelationshipFilter) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{31}
}

func (x *RelationshipFilter) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *RelationshipFilter) GetOptionalResourceId() string {
	if x != nil {
		return x.OptionalResourceId
	}
	return ""
}

func (x *RelationshipFilter) GetOptionalResourceIdPrefix() string {
	if x != nil {
		return x.OptionalResourceIdPrefix
	}
	return ""
}

func (x *RelationshipFilter) GetOptionalRelation() string {
	if x != nil {
		return x.OptionalRelation
	}
	return ""
}

func (x *RelationshipFilter) GetOptionalSubjectFilter() *SubjectFilter {
	if x != nil {
		return x.OptionalSubjectFilter
	}
	return nil
}

// SubjectFilter specifies a filter on the subject of a relationship.
//
// subject_type is required and all other fields are optional, and will not
// impose any additional requirements if left unspecified.
type SubjectFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubjectType       string                        `protobuf:"bytes,1,opt,name=subject_type,json=subjectType,proto3" json:"subject_type,omitempty"`
	OptionalSubjectId string                        `protobuf:"bytes,2,opt,name=optional_subject_id,json=optionalSubjectId,proto3" json:"optional_subject_id,omitempty"`
	OptionalRelation  *SubjectFilter_RelationFilter `protobuf:"bytes,3,opt,name=optional_relation,json=optionalRelation,proto3" json:"optional_relation,omitempty"`
}

func (x *SubjectFilter) Reset() {
	*x = SubjectFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubjectFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubjectFilter) ProtoMessage() {}

func (x *SubjectFilter) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubjectFilter.ProtoReflect.Descriptor instead.
func (*SubjectFilter) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{32}
}

func (x *SubjectFilter) GetSubjectType() string {
	if x != nil {
		return x.SubjectType
	}
	return ""
}

func (x *SubjectFilter) GetOptionalSubjectId() string {
	if x != nil {
		return x.OptionalSubjectId
	}
	return ""
}

func (x *SubjectFilter) GetOptionalRelation() *SubjectFilter_RelationFilter {
	if x != nil {
		return x.OptionalRelation
	}
	return nil
}

type AllowedRelation_PublicWildcard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AllowedRelation_PublicWildcard) Reset() {
	*x = AllowedRelation_PublicWildcard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllowedRelation_PublicWildcard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllowedRelation_PublicWildcard) ProtoMessage() {}

func (x *AllowedRelation_PublicWildcard) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllowedRelation_PublicWildcard.ProtoReflect.Descriptor instead.
func (*AllowedRelation_PublicWildcard) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{20, 0}
}

type SetOperation_Child struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to ChildType:
	//
	//	*SetOperation_Child_XThis
	//	*SetOperation_Child_ComputedUserset
	//	*SetOperation_Child_TupleToUserset
	//	*SetOperation_Child_UsersetRewrite
	//	*SetOperation_Child_FunctionedTupleToUserset
	//	*SetOperation_Child_XNil
	ChildType      isSetOperation_Child_ChildType `protobuf_oneof:"child_type"`
	SourcePosition *SourcePosition                `protobuf:"bytes,5,opt,name=source_position,json=sourcePosition,proto3" json:"source_position,omitempty"`
	// *
	// operation_path (if specified) is the *unique* ID for the set operation in the permission
	// definition. It is a heirarchy representing the position of the operation under its parent
	// operation. For example, the operation path of an operation which is the third child of the
	// fourth top-level operation, will be `3,2`.
	OperationPath []uint32 `protobuf:"varint,7,rep,packed,name=operation_path,json=operationPath,proto3" json:"operation_path,omitempty"`
}

func (x *SetOperation_Child) Reset() {
	*x = SetOperation_Child{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetOperation_Child) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetOperation_Child) ProtoMessage() {}

func (x *SetOperation_Child) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetOperation_Child.ProtoReflect.Descriptor instead.
func (*SetOperation_Child) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{24, 0}
}

func (m *SetOperation_Child) GetChildType() isSetOperation_Child_ChildType {
	if m != nil {
		return m.ChildType
	}
	return nil
}

func (x *SetOperation_Child) GetXThis() *SetOperation_Child_This {
	if x, ok := x.GetChildType().(*SetOperation_Child_XThis); ok {
		return x.XThis
	}
	return nil
}

func (x *SetOperation_Child) GetComputedUserset() *ComputedUserset {
	if x, ok := x.GetChildType().(*SetOperation_Child_ComputedUserset); ok {
		return x.ComputedUserset
	}
	return nil
}

func (x *SetOperation_Child) GetTupleToUserset() *TupleToUserset {
	if x, ok := x.GetChildType().(*SetOperation_Child_TupleToUserset); ok {
		return x.TupleToUserset
	}
	return nil
}

func (x *SetOperation_Child) GetUsersetRewrite() *UsersetRewrite {
	if x, ok := x.GetChildType().(*SetOperation_Child_UsersetRewrite); ok {
		return x.UsersetRewrite
	}
	return nil
}

func (x *SetOperation_Child) GetFunctionedTupleToUserset() *FunctionedTupleToUserset {
	if x, ok := x.GetChildType().(*SetOperation_Child_FunctionedTupleToUserset); ok {
		return x.FunctionedTupleToUserset
	}
	return nil
}

func (x *SetOperation_Child) GetXNil() *SetOperation_Child_Nil {
	if x, ok := x.GetChildType().(*SetOperation_Child_XNil); ok {
		return x.XNil
	}
	return nil
}

func (x *SetOperation_Child) GetSourcePosition() *SourcePosition {
	if x != nil {
		return x.SourcePosition
	}
	return nil
}

func (x *SetOperation_Child) GetOperationPath() []uint32 {
	if x != nil {
		return x.OperationPath
	}
	return nil
}

type isSetOperation_Child_ChildType interface {
	isSetOperation_Child_ChildType()
}

type SetOperation_Child_XThis struct {
	XThis *SetOperation_Child_This `protobuf:"bytes,1,opt,name=_this,json=This,proto3,oneof"`
}

type SetOperation_Child_ComputedUserset struct {
	ComputedUserset *ComputedUserset `protobuf:"bytes,2,opt,name=computed_userset,json=computedUserset,proto3,oneof"`
}

type SetOperation_Child_TupleToUserset struct {
	TupleToUserset *TupleToUserset `protobuf:"bytes,3,opt,name=tuple_to_userset,json=tupleToUserset,proto3,oneof"`
}

type SetOperation_Child_UsersetRewrite struct {
	UsersetRewrite *UsersetRewrite `protobuf:"bytes,4,opt,name=userset_rewrite,json=usersetRewrite,proto3,oneof"`
}

type SetOperation_Child_FunctionedTupleToUserset struct {
	FunctionedTupleToUserset *FunctionedTupleToUserset `protobuf:"bytes,8,opt,name=functioned_tuple_to_userset,json=functionedTupleToUserset,proto3,oneof"`
}

type SetOperation_Child_XNil struct {
	XNil *SetOperation_Child_Nil `protobuf:"bytes,6,opt,name=_nil,json=Nil,proto3,oneof"`
}

func (*SetOperation_Child_XThis) isSetOperation_Child_ChildType() {}

func (*SetOperation_Child_ComputedUserset) isSetOperation_Child_ChildType() {}

func (*SetOperation_Child_TupleToUserset) isSetOperation_Child_ChildType() {}

func (*SetOperation_Child_UsersetRewrite) isSetOperation_Child_ChildType() {}

func (*SetOperation_Child_FunctionedTupleToUserset) isSetOperation_Child_ChildType() {}

func (*SetOperation_Child_XNil) isSetOperation_Child_ChildType() {}

type SetOperation_Child_This struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetOperation_Child_This) Reset() {
	*x = SetOperation_Child_This{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetOperation_Child_This) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetOperation_Child_This) ProtoMessage() {}

func (x *SetOperation_Child_This) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetOperation_Child_This.ProtoReflect.Descriptor instead.
func (*SetOperation_Child_This) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{24, 0, 0}
}

type SetOperation_Child_Nil struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetOperation_Child_Nil) Reset() {
	*x = SetOperation_Child_Nil{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetOperation_Child_Nil) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetOperation_Child_Nil) ProtoMessage() {}

func (x *SetOperation_Child_Nil) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetOperation_Child_Nil.ProtoReflect.Descriptor instead.
func (*SetOperation_Child_Nil) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{24, 0, 1}
}

type TupleToUserset_Tupleset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Relation string `protobuf:"bytes,1,opt,name=relation,proto3" json:"relation,omitempty"`
}

func (x *TupleToUserset_Tupleset) Reset() {
	*x = TupleToUserset_Tupleset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TupleToUserset_Tupleset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TupleToUserset_Tupleset) ProtoMessage() {}

func (x *TupleToUserset_Tupleset) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TupleToUserset_Tupleset.ProtoReflect.Descriptor instead.
func (*TupleToUserset_Tupleset) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{25, 0}
}

func (x *TupleToUserset_Tupleset) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

type FunctionedTupleToUserset_Tupleset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Relation string `protobuf:"bytes,1,opt,name=relation,proto3" json:"relation,omitempty"`
}

func (x *FunctionedTupleToUserset_Tupleset) Reset() {
	*x = FunctionedTupleToUserset_Tupleset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FunctionedTupleToUserset_Tupleset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunctionedTupleToUserset_Tupleset) ProtoMessage() {}

func (x *FunctionedTupleToUserset_Tupleset) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunctionedTupleToUserset_Tupleset.ProtoReflect.Descriptor instead.
func (*FunctionedTupleToUserset_Tupleset) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{26, 0}
}

func (x *FunctionedTupleToUserset_Tupleset) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

type SubjectFilter_RelationFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Relation string `protobuf:"bytes,1,opt,name=relation,proto3" json:"relation,omitempty"`
}

func (x *SubjectFilter_RelationFilter) Reset() {
	*x = SubjectFilter_RelationFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_v1_core_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubjectFilter_RelationFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubjectFilter_RelationFilter) ProtoMessage() {}

func (x *SubjectFilter_RelationFilter) ProtoReflect() protoreflect.Message {
	mi := &file_core_v1_core_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubjectFilter_RelationFilter.ProtoReflect.Descriptor instead.
func (*SubjectFilter_RelationFilter) Descriptor() ([]byte, []int) {
	return file_core_v1_core_proto_rawDescGZIP(), []int{32, 0}
}

func (x *SubjectFilter_RelationFilter) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

var File_core_v1_core_proto protoreflect.FileDescriptor

var file_core_v1_core_proto_rawDesc = []byte{
	0x0a, 0x12, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x19, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61,
	0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x88, 0x03, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x75, 0x70,
	0x6c, 0x65, 0x12, 0x58, 0x0a, 0x15, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x61,
	0x6e, 0x64, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x41, 0x6e, 0x64, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x41, 0x6e, 0x64, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x07,
	0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x41, 0x6e,
	0x64, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x3f, 0x0a, 0x06,
	0x63, 0x61, 0x76, 0x65, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x75, 0x61,
	0x6c, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x61, 0x76, 0x65, 0x61, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x00, 0x52, 0x06, 0x63, 0x61, 0x76, 0x65, 0x61, 0x74, 0x12, 0x46, 0x0a,
	0x09, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x69, 0x74, 0x79,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x00, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x65,
	0x67, 0x72, 0x69, 0x74, 0x79, 0x12, 0x54, 0x0a, 0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x16, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x7b, 0x0a, 0x15, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x49, 0x6e, 0x74, 0x65, 0x67,
	0x72, 0x69, 0x74, 0x79, 0x12, 0x15, 0x0a, 0x06, 0x6b, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6b, 0x65, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x68,
	0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12,
	0x37, 0x0a, 0x09, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08,
	0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x41, 0x74, 0x22, 0xab, 0x01, 0x0a, 0x14, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x61, 0x76, 0x65, 0x61,
	0x74, 0x12, 0x56, 0x0a, 0x0b, 0x63, 0x61, 0x76, 0x65, 0x61, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x35, 0xfa, 0x42, 0x32, 0x72, 0x30, 0x28, 0x80, 0x01,
	0x32, 0x2b, 0x5e, 0x28, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x30, 0x2d, 0x39, 0x5f,
	0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x30, 0x2d, 0x39, 0x2f, 0x5f, 0x7c, 0x2d, 0x5d,
	0x7b, 0x30, 0x2c, 0x31, 0x32, 0x37, 0x7d, 0x29, 0x7c, 0x5c, 0x2a, 0x29, 0x24, 0x52, 0x0a, 0x63,
	0x61, 0x76, 0x65, 0x61, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x00, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0xd4, 0x03, 0x0a, 0x10, 0x43, 0x61, 0x76, 0x65, 0x61,
	0x74, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x35, 0xfa, 0x42, 0x32, 0x72, 0x30,
	0x28, 0x80, 0x01, 0x32, 0x2b, 0x5e, 0x28, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x30,
	0x2d, 0x39, 0x5f, 0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x30, 0x2d, 0x39, 0x2f, 0x5f,
	0x7c, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x31, 0x32, 0x37, 0x7d, 0x29, 0x7c, 0x5c, 0x2a, 0x29, 0x24,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x15, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x69, 0x7a, 0x65, 0x64, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x7a, 0x05, 0x10, 0x00, 0x18, 0x80,
	0x20, 0x52, 0x14, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x45, 0x78, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x62, 0x0a, 0x0f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x76, 0x65, 0x61,
	0x74, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x9a, 0x01, 0x04, 0x08, 0x01, 0x10, 0x14, 0x52, 0x0e, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x08, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x0f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x5f, 0x0a, 0x13,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x76, 0x65, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7d, 0x0a,
	0x13, 0x43, 0x61, 0x76, 0x65, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x49, 0x0a, 0x0b, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x61, 0x76, 0x65, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x00, 0x10, 0x01,
	0x52, 0x0a, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x91, 0x02, 0x0a,
	0x11, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x41, 0x6e, 0x64, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x66, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x48, 0xfa, 0x42, 0x45, 0x72, 0x43, 0x28, 0x80, 0x01, 0x32,
	0x3e, 0x5e, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5f,
	0x5d, 0x7b, 0x31, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x2f,
	0x29, 0x2a, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5f, 0x5d,
	0x7b, 0x31, 0x2c, 0x36, 0x32, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x24, 0x52,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x46, 0x0a, 0x09, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x29, 0xfa,
	0x42, 0x26, 0x72, 0x24, 0x28, 0x80, 0x08, 0x32, 0x1f, 0x5e, 0x28, 0x28, 0x5b, 0x61, 0x2d, 0x7a,
	0x41, 0x2d, 0x5a, 0x30, 0x2d, 0x39, 0x2f, 0x5f, 0x7c, 0x5c, 0x2d, 0x3d, 0x2b, 0x5d, 0x7b, 0x31,
	0x2c, 0x7d, 0x29, 0x7c, 0x5c, 0x2a, 0x29, 0x24, 0x52, 0x08, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x4c, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x30, 0xfa, 0x42, 0x2d, 0x72, 0x2b, 0x28, 0x40, 0x32, 0x27, 0x5e,
	0x28, 0x5c, 0x2e, 0x5c, 0x2e, 0x5c, 0x2e, 0x7c, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x5f, 0x5d, 0x7b, 0x31, 0x2c, 0x36, 0x32, 0x7d, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x5d, 0x29, 0x24, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0xc9, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x66, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x48, 0xfa, 0x42, 0x45, 0x72, 0x43,
	0x28, 0x80, 0x01, 0x32, 0x3e, 0x5e, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x5f, 0x5d, 0x7b, 0x31, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5d, 0x2f, 0x29, 0x2a, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5f, 0x5d, 0x7b, 0x31, 0x2c, 0x36, 0x32, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x24, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x4c,
	0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x30, 0xfa, 0x42, 0x2d, 0x72, 0x2b, 0x28, 0x40, 0x32, 0x27, 0x5e, 0x28, 0x5c, 0x2e, 0x5c,
	0x2e, 0x5c, 0x2e, 0x7c, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x5f, 0x5d, 0x7b, 0x31, 0x2c, 0x36, 0x32, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x29, 0x24, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x27, 0x0a, 0x06,
	0x5a, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x12, 0x1d, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xda, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x4e, 0x0a,
	0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x26, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a,
	0x05, 0x74, 0x75, 0x70, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x75, 0x70, 0x6c, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05,
	0x74, 0x75, 0x70, 0x6c, 0x65, 0x22, 0x3b, 0x0a, 0x09, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x0a, 0x0a, 0x06, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x54,
	0x4f, 0x55, 0x43, 0x48, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45,
	0x10, 0x03, 0x22, 0xa9, 0x02, 0x0a, 0x15, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x75, 0x70, 0x6c, 0x65, 0x54, 0x72, 0x65, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x4b, 0x0a, 0x11,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x6e, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x65, 0x74, 0x48, 0x00, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a, 0x09, 0x6c, 0x65, 0x61,
	0x66, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x53, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x48, 0x00, 0x52, 0x08, 0x6c, 0x65, 0x61, 0x66, 0x4e, 0x6f, 0x64,
	0x65, 0x12, 0x36, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x41, 0x6e, 0x64, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x08, 0x65, 0x78, 0x70, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x12, 0x46, 0x0a, 0x11, 0x63, 0x61, 0x76,
	0x65, 0x61, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x76, 0x65, 0x61, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x10, 0x63, 0x61, 0x76, 0x65, 0x61, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xe2,
	0x01, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x12, 0x44, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x0b,
	0x63, 0x68, 0x69, 0x6c, 0x64, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x54, 0x72, 0x65, 0x65, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x0a, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x22, 0x44, 0x0a,
	0x09, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x55, 0x4e, 0x49, 0x4f, 0x4e,
	0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x53, 0x45, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x45, 0x58, 0x43, 0x4c, 0x55, 0x53, 0x49, 0x4f,
	0x4e, 0x10, 0x03, 0x22, 0x8d, 0x01, 0x0a, 0x0d, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x53, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x34, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x41, 0x6e, 0x64, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x46, 0x0a, 0x11, 0x63,
	0x61, 0x76, 0x65, 0x61, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x61, 0x76, 0x65, 0x61, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x10, 0x63, 0x61, 0x76, 0x65, 0x61, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0x44, 0x0a, 0x0e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x53, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x32, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52,
	0x08, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x22, 0xb8, 0x01, 0x0a, 0x08, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0xab, 0x01, 0x0a, 0x10, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x42, 0x6a, 0xfa, 0x42, 0x67, 0x92, 0x01, 0x64, 0x08,
	0x01, 0x22, 0x60, 0x8a, 0x01, 0x02, 0x10, 0x01, 0xa2, 0x01, 0x58, 0x08, 0x01, 0x12, 0x26, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x69, 0x6d, 0x70, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x63, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x69, 0x6d, 0x70, 0x6c, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x0f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x22, 0x93, 0x02, 0x0a, 0x13, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5c, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x48, 0xfa, 0x42, 0x45, 0x72,
	0x43, 0x28, 0x80, 0x01, 0x32, 0x3e, 0x5e, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x5f, 0x5d, 0x7b, 0x31, 0x2c, 0x36, 0x32, 0x7d, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x5d, 0x2f, 0x29, 0x2a, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x5f, 0x5d, 0x7b, 0x31, 0x2c, 0x36, 0x32, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5d, 0x24, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x08, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x0f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9c, 0x03, 0x0a, 0x08, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x27, 0xfa, 0x42, 0x24, 0x72, 0x22, 0x28, 0x40, 0x32, 0x1e,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5f, 0x5d, 0x7b,
	0x31, 0x2c, 0x36, 0x32, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x24, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x5f,
	0x72, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x52,
	0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x52, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x52,
	0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x12, 0x43, 0x0a, 0x10, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x74, 0x79, 0x70, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x08, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x0f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x11,
	0x61, 0x6c, 0x69, 0x61, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x61, 0x6e,
	0x6f, 0x6e, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x61, 0x6e, 0x6f, 0x6e, 0x69, 0x63, 0x61,
	0x6c, 0x43, 0x61, 0x63, 0x68, 0x65, 0x4b, 0x65, 0x79, 0x22, 0xf4, 0x03, 0x0a, 0x11, 0x52, 0x65,
	0x61, 0x63, 0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x47, 0x72, 0x61, 0x70, 0x68, 0x12,
	0x77, 0x0a, 0x1b, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x5f, 0x62,
	0x79, 0x5f, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x47, 0x72, 0x61, 0x70, 0x68,
	0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x53, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x18,
	0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x53, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x1f, 0x65, 0x6e, 0x74,
	0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x5f, 0x62, 0x79, 0x5f, 0x73, 0x75, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61,
	0x63, 0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x47, 0x72, 0x61, 0x70, 0x68, 0x2e, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x53, 0x75, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x1c, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x53,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x6d,
	0x0a, 0x1d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x53,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x36, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x63, 0x68,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x71, 0x0a,
	0x21, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x53, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x36, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x61, 0x63, 0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0xc6, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x41, 0x0a, 0x0b,
	0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x63,
	0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x52, 0x0b, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x45, 0x0a, 0x10, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0f, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xce, 0x04, 0x0a, 0x16, 0x52, 0x65,
	0x61, 0x63, 0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61,
	0x63, 0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04,
	0x6b, 0x69, 0x6e, 0x64, 0x12, 0x43, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5b, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x36, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x63, 0x68,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x74, 0x75, 0x70, 0x6c, 0x65, 0x73,
	0x65, 0x74, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x74, 0x75, 0x70, 0x6c, 0x65, 0x73, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x19, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x7a, 0x0a, 0x1a, 0x52, 0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x17, 0x0a,
	0x13, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x50,
	0x4f, 0x49, 0x4e, 0x54, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54,
	0x45, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x45, 0x54, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59,
	0x50, 0x4f, 0x49, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x54, 0x55, 0x50, 0x4c, 0x45,
	0x53, 0x45, 0x54, 0x5f, 0x54, 0x4f, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x45, 0x54, 0x5f, 0x45,
	0x4e, 0x54, 0x52, 0x59, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x10, 0x02, 0x22, 0x57, 0x0a, 0x16, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x41, 0x43, 0x48, 0x41, 0x42,
	0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x52,
	0x45, 0x53, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x44, 0x49, 0x52, 0x45, 0x43,
	0x54, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x55,
	0x4c, 0x54, 0x10, 0x01, 0x4a, 0x04, 0x08, 0x03, 0x10, 0x04, 0x22, 0x65, 0x0a, 0x0f, 0x54, 0x79,
	0x70, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x52, 0x0a,
	0x18, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x64, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x16, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x95, 0x04, 0x0a, 0x0f, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x66, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x48, 0xfa, 0x42, 0x45, 0x72, 0x43, 0x28,
	0x80, 0x01, 0x32, 0x3e, 0x5e, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5f, 0x5d, 0x7b, 0x31, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x2f, 0x29, 0x2a, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5f, 0x5d, 0x7b, 0x31, 0x2c, 0x36, 0x32, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x5d, 0x24, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x4e, 0x0a,
	0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x30, 0xfa, 0x42, 0x2d, 0x72, 0x2b, 0x28, 0x40, 0x32, 0x27, 0x5e, 0x28, 0x5c, 0x2e, 0x5c, 0x2e,
	0x5c, 0x2e, 0x7c, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5f,
	0x5d, 0x7b, 0x31, 0x2c, 0x36, 0x32, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29,
	0x24, 0x48, 0x00, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x52, 0x0a,
	0x0f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x77, 0x69, 0x6c, 0x64, 0x63, 0x61, 0x72, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x57, 0x69, 0x6c, 0x64, 0x63, 0x61, 0x72, 0x64, 0x48,
	0x00, 0x52, 0x0e, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x57, 0x69, 0x6c, 0x64, 0x63, 0x61, 0x72,
	0x64, 0x12, 0x40, 0x0a, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f,
	0x63, 0x61, 0x76, 0x65, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x43, 0x61,
	0x76, 0x65, 0x61, 0x74, 0x52, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x43, 0x61,
	0x76, 0x65, 0x61, 0x74, 0x12, 0x49, 0x0a, 0x13, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x69, 0x74, 0x52, 0x12, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a,
	0x10, 0x0a, 0x0e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x57, 0x69, 0x6c, 0x64, 0x63, 0x61, 0x72,
	0x64, 0x42, 0x16, 0x0a, 0x14, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x72,
	0x5f, 0x77, 0x69, 0x6c, 0x64, 0x63, 0x61, 0x72, 0x64, 0x22, 0x11, 0x0a, 0x0f, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x69, 0x74, 0x22, 0x30, 0x0a, 0x0d,
	0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x43, 0x61, 0x76, 0x65, 0x61, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x61, 0x76, 0x65, 0x61, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x76, 0x65, 0x61, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xad,
	0x02, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74,
	0x65, 0x12, 0x37, 0x0a, 0x05, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x48, 0x00, 0x52, 0x05, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x0c, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x48, 0x00, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x3f, 0x0a, 0x09, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x09, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x18, 0x0a, 0x11, 0x72, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xb2,
	0x05, 0x0a, 0x0c, 0x53, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x42, 0x0a, 0x05, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x42, 0x0f, 0xfa, 0x42, 0x0c,
	0x92, 0x01, 0x09, 0x08, 0x01, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x63, 0x68,
	0x69, 0x6c, 0x64, 0x1a, 0xdd, 0x04, 0x0a, 0x05, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x12, 0x37, 0x0a,
	0x05, 0x5f, 0x74, 0x68, 0x69, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x2e, 0x54, 0x68, 0x69, 0x73, 0x48, 0x00,
	0x52, 0x04, 0x54, 0x68, 0x69, 0x73, 0x12, 0x4f, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74,
	0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x75,
	0x74, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x12, 0x4d, 0x0a, 0x10, 0x74, 0x75, 0x70, 0x6c, 0x65,
	0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x75, 0x70, 0x6c,
	0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x0e, 0x74, 0x75, 0x70, 0x6c, 0x65, 0x54, 0x6f, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x12, 0x4c, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x65,
	0x74, 0x5f, 0x72, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x65,
	0x74, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x48, 0x00, 0x52, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x52, 0x65, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x12, 0x6c, 0x0a, 0x1b, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x65, 0x64, 0x5f, 0x74, 0x75, 0x70, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x65, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x64, 0x54, 0x75,
	0x70, 0x6c, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x18, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x65, 0x64, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x65, 0x74, 0x12, 0x34, 0x0a, 0x04, 0x5f, 0x6e, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x2e, 0x4e, 0x69,
	0x6c, 0x48, 0x00, 0x52, 0x03, 0x4e, 0x69, 0x6c, 0x12, 0x40, 0x0a, 0x0f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0d, 0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x74,
	0x68, 0x1a, 0x06, 0x0a, 0x04, 0x54, 0x68, 0x69, 0x73, 0x1a, 0x05, 0x0a, 0x03, 0x4e, 0x69, 0x6c,
	0x42, 0x11, 0x0a, 0x0a, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x03,
	0xf8, 0x42, 0x01, 0x22, 0xba, 0x02, 0x0a, 0x0e, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x54, 0x6f, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x12, 0x46, 0x0a, 0x08, 0x74, 0x75, 0x70, 0x6c, 0x65, 0x73,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x73, 0x65,
	0x74, 0x2e, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x73, 0x65, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x74, 0x75, 0x70, 0x6c, 0x65, 0x73, 0x65, 0x74, 0x12, 0x4d,
	0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73,
	0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x65, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0f, 0x63, 0x6f,
	0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x12, 0x40, 0x0a,
	0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x1a,
	0x4f, 0x0a, 0x08, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x73, 0x65, 0x74, 0x12, 0x43, 0x0a, 0x08, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x27, 0xfa,
	0x42, 0x24, 0x72, 0x22, 0x28, 0x40, 0x32, 0x1e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5f, 0x5d, 0x7b, 0x31, 0x2c, 0x36, 0x32, 0x7d, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x24, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0xec, 0x03, 0x0a, 0x18, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x64, 0x54,
	0x75, 0x70, 0x6c, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x12, 0x52, 0x0a,
	0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2a, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x65, 0x64, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x65, 0x74, 0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x50, 0x0a, 0x08, 0x74, 0x75, 0x70, 0x6c, 0x65, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x75,
	0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x64, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x54, 0x6f, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x2e, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x73, 0x65, 0x74, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x74, 0x75, 0x70, 0x6c, 0x65,
	0x73, 0x65, 0x74, 0x12, 0x4d, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x65, 0x74, 0x12, 0x40, 0x0a, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x4f, 0x0a, 0x08, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x73, 0x65, 0x74,
	0x12, 0x43, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x27, 0xfa, 0x42, 0x24, 0x72, 0x22, 0x28, 0x40, 0x32, 0x1e, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5f, 0x5d, 0x7b, 0x31, 0x2c, 0x36,
	0x32, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x24, 0x52, 0x08, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x48, 0x0a, 0x08, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x55, 0x4e, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x46,
	0x55, 0x4e, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4e, 0x59, 0x10, 0x01, 0x12, 0x10, 0x0a,
	0x0c, 0x46, 0x55, 0x4e, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x02, 0x22,
	0x91, 0x02, 0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x65, 0x74, 0x12, 0x41, 0x0a, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x73, 0x65, 0x74, 0x2e, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x43, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x27, 0xfa, 0x42, 0x24, 0x72, 0x22, 0x28,
	0x40, 0x32, 0x1e, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x5f, 0x5d, 0x7b, 0x31, 0x2c, 0x36, 0x32, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x24, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x0f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x34, 0x0a,
	0x06, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x55, 0x50, 0x4c, 0x45,
	0x5f, 0x4f, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x55, 0x50,
	0x4c, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x45, 0x54, 0x5f, 0x4f, 0x42, 0x4a, 0x45, 0x43,
	0x54, 0x10, 0x01, 0x22, 0x8a, 0x01, 0x0a, 0x0e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x18, 0x7a, 0x65, 0x72, 0x6f, 0x5f, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x65, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x15, 0x7a, 0x65, 0x72, 0x6f, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x65, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x3f, 0x0a, 0x1c, 0x7a, 0x65, 0x72, 0x6f, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x64, 0x5f,
	0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x19, 0x7a, 0x65, 0x72, 0x6f, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x65, 0x64, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x9c, 0x01, 0x0a, 0x10, 0x43, 0x61, 0x76, 0x65, 0x61, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x76, 0x65, 0x61, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x00, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x37, 0x0a, 0x06, 0x63, 0x61, 0x76, 0x65, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x61, 0x76, 0x65, 0x61, 0x74, 0x48, 0x00,
	0x52, 0x06, 0x63, 0x61, 0x76, 0x65, 0x61, 0x74, 0x42, 0x15, 0x0a, 0x13, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x72, 0x5f, 0x63, 0x61, 0x76, 0x65, 0x61, 0x74, 0x22,
	0xb0, 0x01, 0x0a, 0x0f, 0x43, 0x61, 0x76, 0x65, 0x61, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x02, 0x6f, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x22, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x76, 0x65, 0x61, 0x74,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x02, 0x6f, 0x70, 0x12, 0x35, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64,
	0x72, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x76, 0x65, 0x61, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x22, 0x32,
	0x0a, 0x09, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0b, 0x0a, 0x07, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x52, 0x10, 0x01,
	0x12, 0x07, 0x0a, 0x03, 0x41, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x4e, 0x4f, 0x54,
	0x10, 0x03, 0x22, 0xee, 0x03, 0x0a, 0x12, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x68, 0x69, 0x70, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x70, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x4b, 0xfa, 0x42, 0x48, 0x72, 0x46, 0x28, 0x80, 0x01, 0x32, 0x41, 0x5e, 0x28, 0x28, 0x5b,
	0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5f, 0x5d, 0x7b, 0x31, 0x2c,
	0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x2f, 0x29, 0x2a, 0x5b, 0x61,
	0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5f, 0x5d, 0x7b, 0x31, 0x2c, 0x36,
	0x32, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0c, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x57, 0x0a, 0x14, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x25, 0xfa, 0x42, 0x22, 0x72, 0x20,
	0x28, 0x80, 0x08, 0x32, 0x1b, 0x5e, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x30, 0x2d,
	0x39, 0x2f, 0x5f, 0x7c, 0x5c, 0x2d, 0x3d, 0x2b, 0x5d, 0x7b, 0x31, 0x2c, 0x7d, 0x29, 0x3f, 0x24,
	0x52, 0x12, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x64, 0x0a, 0x1b, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x70, 0x72, 0x65,
	0x66, 0x69, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x25, 0xfa, 0x42, 0x22, 0x72, 0x20,
	0x28, 0x80, 0x08, 0x32, 0x1b, 0x5e, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x30, 0x2d,
	0x39, 0x2f, 0x5f, 0x7c, 0x5c, 0x2d, 0x3d, 0x2b, 0x5d, 0x7b, 0x31, 0x2c, 0x7d, 0x29, 0x3f, 0x24,
	0x52, 0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x49, 0x64, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x57, 0x0a, 0x11, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x28, 0x40, 0x32, 0x21,
	0x5e, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5f, 0x5d,
	0x7b, 0x31, 0x2c, 0x36, 0x32, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f,
	0x24, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x15, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x22, 0x86, 0x03, 0x0a, 0x0d, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x6b, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x48, 0xfa, 0x42, 0x45,
	0x72, 0x43, 0x28, 0x80, 0x01, 0x32, 0x3e, 0x5e, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5f, 0x5d, 0x7b, 0x31, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x2f, 0x29, 0x2a, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x5f, 0x5d, 0x7b, 0x31, 0x2c, 0x36, 0x32, 0x7d, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x5d, 0x24, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x5a, 0x0a, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x2a, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x28, 0x80, 0x08, 0x32, 0x20, 0x5e, 0x28, 0x28, 0x5b, 0x61,
	0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x30, 0x2d, 0x39, 0x2f, 0x5f, 0x7c, 0x5c, 0x2d, 0x3d, 0x2b, 0x5d,
	0x7b, 0x31, 0x2c, 0x7d, 0x29, 0x7c, 0x5c, 0x2a, 0x29, 0x3f, 0x24, 0x52, 0x11, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x52,
	0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x1a, 0x58, 0x0a, 0x0e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x46, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x28, 0x40, 0x32,
	0x21, 0x5e, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5f,
	0x5d, 0x7b, 0x31, 0x2c, 0x36, 0x32, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29,
	0x3f, 0x24, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x8a, 0x01, 0x0a,
	0x0b, 0x63, 0x6f, 0x6d, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x42, 0x09, 0x43, 0x6f,
	0x72, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x33, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x7a, 0x65, 0x64, 0x2f, 0x73, 0x70,
	0x69, 0x63, 0x65, 0x64, 0x62, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x63, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x6f, 0x72, 0x65, 0x76, 0x31, 0xa2, 0x02,
	0x03, 0x43, 0x58, 0x58, 0xaa, 0x02, 0x07, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x56, 0x31, 0xca, 0x02,
	0x07, 0x43, 0x6f, 0x72, 0x65, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x13, 0x43, 0x6f, 0x72, 0x65, 0x5c,
	0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02,
	0x08, 0x43, 0x6f, 0x72, 0x65, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_core_v1_core_proto_rawDescOnce sync.Once
	file_core_v1_core_proto_rawDescData = file_core_v1_core_proto_rawDesc
)

func file_core_v1_core_proto_rawDescGZIP() []byte {
	file_core_v1_core_proto_rawDescOnce.Do(func() {
		file_core_v1_core_proto_rawDescData = protoimpl.X.CompressGZIP(file_core_v1_core_proto_rawDescData)
	})
	return file_core_v1_core_proto_rawDescData
}

var file_core_v1_core_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_core_v1_core_proto_msgTypes = make([]protoimpl.MessageInfo, 43)
var file_core_v1_core_proto_goTypes = []any{
	(RelationTupleUpdate_Operation)(0),                     // 0: core.v1.RelationTupleUpdate.Operation
	(SetOperationUserset_Operation)(0),                     // 1: core.v1.SetOperationUserset.Operation
	(ReachabilityEntrypoint_ReachabilityEntrypointKind)(0), // 2: core.v1.ReachabilityEntrypoint.ReachabilityEntrypointKind
	(ReachabilityEntrypoint_EntrypointResultStatus)(0),     // 3: core.v1.ReachabilityEntrypoint.EntrypointResultStatus
	(FunctionedTupleToUserset_Function)(0),                 // 4: core.v1.FunctionedTupleToUserset.Function
	(ComputedUserset_Object)(0),                            // 5: core.v1.ComputedUserset.Object
	(CaveatOperation_Operation)(0),                         // 6: core.v1.CaveatOperation.Operation
	(*RelationTuple)(nil),                                  // 7: core.v1.RelationTuple
	(*RelationshipIntegrity)(nil),                          // 8: core.v1.RelationshipIntegrity
	(*ContextualizedCaveat)(nil),                           // 9: core.v1.ContextualizedCaveat
	(*CaveatDefinition)(nil),                               // 10: core.v1.CaveatDefinition
	(*CaveatTypeReference)(nil),                            // 11: core.v1.CaveatTypeReference
	(*ObjectAndRelation)(nil),                              // 12: core.v1.ObjectAndRelation
	(*RelationReference)(nil),                              // 13: core.v1.RelationReference
	(*Zookie)(nil),                                         // 14: core.v1.Zookie
	(*RelationTupleUpdate)(nil),                            // 15: core.v1.RelationTupleUpdate
	(*RelationTupleTreeNode)(nil),                          // 16: core.v1.RelationTupleTreeNode
	(*SetOperationUserset)(nil),                            // 17: core.v1.SetOperationUserset
	(*DirectSubject)(nil),                                  // 18: core.v1.DirectSubject
	(*DirectSubjects)(nil),                                 // 19: core.v1.DirectSubjects
	(*Metadata)(nil),                                       // 20: core.v1.Metadata
	(*NamespaceDefinition)(nil),                            // 21: core.v1.NamespaceDefinition
	(*Relation)(nil),                                       // 22: core.v1.Relation
	(*ReachabilityGraph)(nil),                              // 23: core.v1.ReachabilityGraph
	(*ReachabilityEntrypoints)(nil),                        // 24: core.v1.ReachabilityEntrypoints
	(*ReachabilityEntrypoint)(nil),                         // 25: core.v1.ReachabilityEntrypoint
	(*TypeInformation)(nil),                                // 26: core.v1.TypeInformation
	(*AllowedRelation)(nil),                                // 27: core.v1.AllowedRelation
	(*ExpirationTrait)(nil),                                // 28: core.v1.ExpirationTrait
	(*AllowedCaveat)(nil),                                  // 29: core.v1.AllowedCaveat
	(*UsersetRewrite)(nil),                                 // 30: core.v1.UsersetRewrite
	(*SetOperation)(nil),                                   // 31: core.v1.SetOperation
	(*TupleToUserset)(nil),                                 // 32: core.v1.TupleToUserset
	(*FunctionedTupleToUserset)(nil),                       // 33: core.v1.FunctionedTupleToUserset
	(*ComputedUserset)(nil),                                // 34: core.v1.ComputedUserset
	(*SourcePosition)(nil),                                 // 35: core.v1.SourcePosition
	(*CaveatExpression)(nil),                               // 36: core.v1.CaveatExpression
	(*CaveatOperation)(nil),                                // 37: core.v1.CaveatOperation
	(*RelationshipFilter)(nil),                             // 38: core.v1.RelationshipFilter
	(*SubjectFilter)(nil),                                  // 39: core.v1.SubjectFilter
	nil,                                                    // 40: core.v1.CaveatDefinition.ParameterTypesEntry
	nil,                                                    // 41: core.v1.ReachabilityGraph.EntrypointsBySubjectTypeEntry
	nil,                                                    // 42: core.v1.ReachabilityGraph.EntrypointsBySubjectRelationEntry
	(*AllowedRelation_PublicWildcard)(nil),                 // 43: core.v1.AllowedRelation.PublicWildcard
	(*SetOperation_Child)(nil),                             // 44: core.v1.SetOperation.Child
	(*SetOperation_Child_This)(nil),                        // 45: core.v1.SetOperation.Child.This
	(*SetOperation_Child_Nil)(nil),                         // 46: core.v1.SetOperation.Child.Nil
	(*TupleToUserset_Tupleset)(nil),                        // 47: core.v1.TupleToUserset.Tupleset
	(*FunctionedTupleToUserset_Tupleset)(nil),              // 48: core.v1.FunctionedTupleToUserset.Tupleset
	(*SubjectFilter_RelationFilter)(nil),                   // 49: core.v1.SubjectFilter.RelationFilter
	(*timestamppb.Timestamp)(nil),                          // 50: google.protobuf.Timestamp
	(*structpb.Struct)(nil),                                // 51: google.protobuf.Struct
	(*anypb.Any)(nil),                                      // 52: google.protobuf.Any
}
var file_core_v1_core_proto_depIdxs = []int32{
	12, // 0: core.v1.RelationTuple.resource_and_relation:type_name -> core.v1.ObjectAndRelation
	12, // 1: core.v1.RelationTuple.subject:type_name -> core.v1.ObjectAndRelation
	9,  // 2: core.v1.RelationTuple.caveat:type_name -> core.v1.ContextualizedCaveat
	8,  // 3: core.v1.RelationTuple.integrity:type_name -> core.v1.RelationshipIntegrity
	50, // 4: core.v1.RelationTuple.optional_expiration_time:type_name -> google.protobuf.Timestamp
	50, // 5: core.v1.RelationshipIntegrity.hashed_at:type_name -> google.protobuf.Timestamp
	51, // 6: core.v1.ContextualizedCaveat.context:type_name -> google.protobuf.Struct
	40, // 7: core.v1.CaveatDefinition.parameter_types:type_name -> core.v1.CaveatDefinition.ParameterTypesEntry
	20, // 8: core.v1.CaveatDefinition.metadata:type_name -> core.v1.Metadata
	35, // 9: core.v1.CaveatDefinition.source_position:type_name -> core.v1.SourcePosition
	11, // 10: core.v1.CaveatTypeReference.child_types:type_name -> core.v1.CaveatTypeReference
	0,  // 11: core.v1.RelationTupleUpdate.operation:type_name -> core.v1.RelationTupleUpdate.Operation
	7,  // 12: core.v1.RelationTupleUpdate.tuple:type_name -> core.v1.RelationTuple
	17, // 13: core.v1.RelationTupleTreeNode.intermediate_node:type_name -> core.v1.SetOperationUserset
	19, // 14: core.v1.RelationTupleTreeNode.leaf_node:type_name -> core.v1.DirectSubjects
	12, // 15: core.v1.RelationTupleTreeNode.expanded:type_name -> core.v1.ObjectAndRelation
	36, // 16: core.v1.RelationTupleTreeNode.caveat_expression:type_name -> core.v1.CaveatExpression
	1,  // 17: core.v1.SetOperationUserset.operation:type_name -> core.v1.SetOperationUserset.Operation
	16, // 18: core.v1.SetOperationUserset.child_nodes:type_name -> core.v1.RelationTupleTreeNode
	12, // 19: core.v1.DirectSubject.subject:type_name -> core.v1.ObjectAndRelation
	36, // 20: core.v1.DirectSubject.caveat_expression:type_name -> core.v1.CaveatExpression
	18, // 21: core.v1.DirectSubjects.subjects:type_name -> core.v1.DirectSubject
	52, // 22: core.v1.Metadata.metadata_message:type_name -> google.protobuf.Any
	22, // 23: core.v1.NamespaceDefinition.relation:type_name -> core.v1.Relation
	20, // 24: core.v1.NamespaceDefinition.metadata:type_name -> core.v1.Metadata
	35, // 25: core.v1.NamespaceDefinition.source_position:type_name -> core.v1.SourcePosition
	30, // 26: core.v1.Relation.userset_rewrite:type_name -> core.v1.UsersetRewrite
	26, // 27: core.v1.Relation.type_information:type_name -> core.v1.TypeInformation
	20, // 28: core.v1.Relation.metadata:type_name -> core.v1.Metadata
	35, // 29: core.v1.Relation.source_position:type_name -> core.v1.SourcePosition
	41, // 30: core.v1.ReachabilityGraph.entrypoints_by_subject_type:type_name -> core.v1.ReachabilityGraph.EntrypointsBySubjectTypeEntry
	42, // 31: core.v1.ReachabilityGraph.entrypoints_by_subject_relation:type_name -> core.v1.ReachabilityGraph.EntrypointsBySubjectRelationEntry
	25, // 32: core.v1.ReachabilityEntrypoints.entrypoints:type_name -> core.v1.ReachabilityEntrypoint
	13, // 33: core.v1.ReachabilityEntrypoints.subject_relation:type_name -> core.v1.RelationReference
	2,  // 34: core.v1.ReachabilityEntrypoint.kind:type_name -> core.v1.ReachabilityEntrypoint.ReachabilityEntrypointKind
	13, // 35: core.v1.ReachabilityEntrypoint.target_relation:type_name -> core.v1.RelationReference
	3,  // 36: core.v1.ReachabilityEntrypoint.result_status:type_name -> core.v1.ReachabilityEntrypoint.EntrypointResultStatus
	27, // 37: core.v1.TypeInformation.allowed_direct_relations:type_name -> core.v1.AllowedRelation
	43, // 38: core.v1.AllowedRelation.public_wildcard:type_name -> core.v1.AllowedRelation.PublicWildcard
	35, // 39: core.v1.AllowedRelation.source_position:type_name -> core.v1.SourcePosition
	29, // 40: core.v1.AllowedRelation.required_caveat:type_name -> core.v1.AllowedCaveat
	28, // 41: core.v1.AllowedRelation.required_expiration:type_name -> core.v1.ExpirationTrait
	31, // 42: core.v1.UsersetRewrite.union:type_name -> core.v1.SetOperation
	31, // 43: core.v1.UsersetRewrite.intersection:type_name -> core.v1.SetOperation
	31, // 44: core.v1.UsersetRewrite.exclusion:type_name -> core.v1.SetOperation
	35, // 45: core.v1.UsersetRewrite.source_position:type_name -> core.v1.SourcePosition
	44, // 46: core.v1.SetOperation.child:type_name -> core.v1.SetOperation.Child
	47, // 47: core.v1.TupleToUserset.tupleset:type_name -> core.v1.TupleToUserset.Tupleset
	34, // 48: core.v1.TupleToUserset.computed_userset:type_name -> core.v1.ComputedUserset
	35, // 49: core.v1.TupleToUserset.source_position:type_name -> core.v1.SourcePosition
	4,  // 50: core.v1.FunctionedTupleToUserset.function:type_name -> core.v1.FunctionedTupleToUserset.Function
	48, // 51: core.v1.FunctionedTupleToUserset.tupleset:type_name -> core.v1.FunctionedTupleToUserset.Tupleset
	34, // 52: core.v1.FunctionedTupleToUserset.computed_userset:type_name -> core.v1.ComputedUserset
	35, // 53: core.v1.FunctionedTupleToUserset.source_position:type_name -> core.v1.SourcePosition
	5,  // 54: core.v1.ComputedUserset.object:type_name -> core.v1.ComputedUserset.Object
	35, // 55: core.v1.ComputedUserset.source_position:type_name -> core.v1.SourcePosition
	37, // 56: core.v1.CaveatExpression.operation:type_name -> core.v1.CaveatOperation
	9,  // 57: core.v1.CaveatExpression.caveat:type_name -> core.v1.ContextualizedCaveat
	6,  // 58: core.v1.CaveatOperation.op:type_name -> core.v1.CaveatOperation.Operation
	36, // 59: core.v1.CaveatOperation.children:type_name -> core.v1.CaveatExpression
	39, // 60: core.v1.RelationshipFilter.optional_subject_filter:type_name -> core.v1.SubjectFilter
	49, // 61: core.v1.SubjectFilter.optional_relation:type_name -> core.v1.SubjectFilter.RelationFilter
	11, // 62: core.v1.CaveatDefinition.ParameterTypesEntry.value:type_name -> core.v1.CaveatTypeReference
	24, // 63: core.v1.ReachabilityGraph.EntrypointsBySubjectTypeEntry.value:type_name -> core.v1.ReachabilityEntrypoints
	24, // 64: core.v1.ReachabilityGraph.EntrypointsBySubjectRelationEntry.value:type_name -> core.v1.ReachabilityEntrypoints
	45, // 65: core.v1.SetOperation.Child._this:type_name -> core.v1.SetOperation.Child.This
	34, // 66: core.v1.SetOperation.Child.computed_userset:type_name -> core.v1.ComputedUserset
	32, // 67: core.v1.SetOperation.Child.tuple_to_userset:type_name -> core.v1.TupleToUserset
	30, // 68: core.v1.SetOperation.Child.userset_rewrite:type_name -> core.v1.UsersetRewrite
	33, // 69: core.v1.SetOperation.Child.functioned_tuple_to_userset:type_name -> core.v1.FunctionedTupleToUserset
	46, // 70: core.v1.SetOperation.Child._nil:type_name -> core.v1.SetOperation.Child.Nil
	35, // 71: core.v1.SetOperation.Child.source_position:type_name -> core.v1.SourcePosition
	72, // [72:72] is the sub-list for method output_type
	72, // [72:72] is the sub-list for method input_type
	72, // [72:72] is the sub-list for extension type_name
	72, // [72:72] is the sub-list for extension extendee
	0,  // [0:72] is the sub-list for field type_name
}

func init() { file_core_v1_core_proto_init() }
func file_core_v1_core_proto_init() {
	if File_core_v1_core_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_core_v1_core_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*RelationTuple); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*RelationshipIntegrity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ContextualizedCaveat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*CaveatDefinition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*CaveatTypeReference); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*ObjectAndRelation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*RelationReference); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*Zookie); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*RelationTupleUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*RelationTupleTreeNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*SetOperationUserset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*DirectSubject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*DirectSubjects); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*Metadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*NamespaceDefinition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*Relation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*ReachabilityGraph); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*ReachabilityEntrypoints); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*ReachabilityEntrypoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*TypeInformation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*AllowedRelation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*ExpirationTrait); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*AllowedCaveat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*UsersetRewrite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*SetOperation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*TupleToUserset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*FunctionedTupleToUserset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*ComputedUserset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*SourcePosition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*CaveatExpression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*CaveatOperation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*RelationshipFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*SubjectFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*AllowedRelation_PublicWildcard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*SetOperation_Child); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*SetOperation_Child_This); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*SetOperation_Child_Nil); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[40].Exporter = func(v any, i int) any {
			switch v := v.(*TupleToUserset_Tupleset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[41].Exporter = func(v any, i int) any {
			switch v := v.(*FunctionedTupleToUserset_Tupleset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_v1_core_proto_msgTypes[42].Exporter = func(v any, i int) any {
			switch v := v.(*SubjectFilter_RelationFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_core_v1_core_proto_msgTypes[9].OneofWrappers = []any{
		(*RelationTupleTreeNode_IntermediateNode)(nil),
		(*RelationTupleTreeNode_LeafNode)(nil),
	}
	file_core_v1_core_proto_msgTypes[20].OneofWrappers = []any{
		(*AllowedRelation_Relation)(nil),
		(*AllowedRelation_PublicWildcard_)(nil),
	}
	file_core_v1_core_proto_msgTypes[23].OneofWrappers = []any{
		(*UsersetRewrite_Union)(nil),
		(*UsersetRewrite_Intersection)(nil),
		(*UsersetRewrite_Exclusion)(nil),
	}
	file_core_v1_core_proto_msgTypes[29].OneofWrappers = []any{
		(*CaveatExpression_Operation)(nil),
		(*CaveatExpression_Caveat)(nil),
	}
	file_core_v1_core_proto_msgTypes[37].OneofWrappers = []any{
		(*SetOperation_Child_XThis)(nil),
		(*SetOperation_Child_ComputedUserset)(nil),
		(*SetOperation_Child_TupleToUserset)(nil),
		(*SetOperation_Child_UsersetRewrite)(nil),
		(*SetOperation_Child_FunctionedTupleToUserset)(nil),
		(*SetOperation_Child_XNil)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_core_v1_core_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   43,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_core_v1_core_proto_goTypes,
		DependencyIndexes: file_core_v1_core_proto_depIdxs,
		EnumInfos:         file_core_v1_core_proto_enumTypes,
		MessageInfos:      file_core_v1_core_proto_msgTypes,
	}.Build()
	File_core_v1_core_proto = out.File
	file_core_v1_core_proto_rawDesc = nil
	file_core_v1_core_proto_goTypes = nil
	file_core_v1_core_proto_depIdxs = nil
}
