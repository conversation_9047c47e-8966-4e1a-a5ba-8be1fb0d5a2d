// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: core/v1/core.proto

package corev1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RelationTuple with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RelationTuple) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RelationTuple with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RelationTupleMultiError, or
// nil if none found.
func (m *RelationTuple) ValidateAll() error {
	return m.validate(true)
}

func (m *RelationTuple) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetResourceAndRelation() == nil {
		err := RelationTupleValidationError{
			field:  "ResourceAndRelation",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetResourceAndRelation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RelationTupleValidationError{
					field:  "ResourceAndRelation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RelationTupleValidationError{
					field:  "ResourceAndRelation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResourceAndRelation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RelationTupleValidationError{
				field:  "ResourceAndRelation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetSubject() == nil {
		err := RelationTupleValidationError{
			field:  "Subject",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSubject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RelationTupleValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RelationTupleValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RelationTupleValidationError{
				field:  "Subject",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCaveat()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RelationTupleValidationError{
					field:  "Caveat",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RelationTupleValidationError{
					field:  "Caveat",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCaveat()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RelationTupleValidationError{
				field:  "Caveat",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIntegrity()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RelationTupleValidationError{
					field:  "Integrity",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RelationTupleValidationError{
					field:  "Integrity",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIntegrity()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RelationTupleValidationError{
				field:  "Integrity",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOptionalExpirationTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RelationTupleValidationError{
					field:  "OptionalExpirationTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RelationTupleValidationError{
					field:  "OptionalExpirationTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOptionalExpirationTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RelationTupleValidationError{
				field:  "OptionalExpirationTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RelationTupleMultiError(errors)
	}

	return nil
}

// RelationTupleMultiError is an error wrapping multiple validation errors
// returned by RelationTuple.ValidateAll() if the designated constraints
// aren't met.
type RelationTupleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RelationTupleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RelationTupleMultiError) AllErrors() []error { return m }

// RelationTupleValidationError is the validation error returned by
// RelationTuple.Validate if the designated constraints aren't met.
type RelationTupleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RelationTupleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RelationTupleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RelationTupleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RelationTupleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RelationTupleValidationError) ErrorName() string { return "RelationTupleValidationError" }

// Error satisfies the builtin error interface
func (e RelationTupleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRelationTuple.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RelationTupleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RelationTupleValidationError{}

// Validate checks the field values on RelationshipIntegrity with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RelationshipIntegrity) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RelationshipIntegrity with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RelationshipIntegrityMultiError, or nil if none found.
func (m *RelationshipIntegrity) ValidateAll() error {
	return m.validate(true)
}

func (m *RelationshipIntegrity) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KeyId

	// no validation rules for Hash

	if all {
		switch v := interface{}(m.GetHashedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RelationshipIntegrityValidationError{
					field:  "HashedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RelationshipIntegrityValidationError{
					field:  "HashedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHashedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RelationshipIntegrityValidationError{
				field:  "HashedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RelationshipIntegrityMultiError(errors)
	}

	return nil
}

// RelationshipIntegrityMultiError is an error wrapping multiple validation
// errors returned by RelationshipIntegrity.ValidateAll() if the designated
// constraints aren't met.
type RelationshipIntegrityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RelationshipIntegrityMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RelationshipIntegrityMultiError) AllErrors() []error { return m }

// RelationshipIntegrityValidationError is the validation error returned by
// RelationshipIntegrity.Validate if the designated constraints aren't met.
type RelationshipIntegrityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RelationshipIntegrityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RelationshipIntegrityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RelationshipIntegrityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RelationshipIntegrityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RelationshipIntegrityValidationError) ErrorName() string {
	return "RelationshipIntegrityValidationError"
}

// Error satisfies the builtin error interface
func (e RelationshipIntegrityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRelationshipIntegrity.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RelationshipIntegrityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RelationshipIntegrityValidationError{}

// Validate checks the field values on ContextualizedCaveat with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ContextualizedCaveat) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContextualizedCaveat with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ContextualizedCaveatMultiError, or nil if none found.
func (m *ContextualizedCaveat) ValidateAll() error {
	return m.validate(true)
}

func (m *ContextualizedCaveat) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetCaveatName()) > 128 {
		err := ContextualizedCaveatValidationError{
			field:  "CaveatName",
			reason: "value length must be at most 128 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ContextualizedCaveat_CaveatName_Pattern.MatchString(m.GetCaveatName()) {
		err := ContextualizedCaveatValidationError{
			field:  "CaveatName",
			reason: "value does not match regex pattern \"^(([a-zA-Z0-9_][a-zA-Z0-9/_|-]{0,127})|\\\\*)$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContextualizedCaveatValidationError{
					field:  "Context",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContextualizedCaveatValidationError{
					field:  "Context",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContextualizedCaveatValidationError{
				field:  "Context",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ContextualizedCaveatMultiError(errors)
	}

	return nil
}

// ContextualizedCaveatMultiError is an error wrapping multiple validation
// errors returned by ContextualizedCaveat.ValidateAll() if the designated
// constraints aren't met.
type ContextualizedCaveatMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContextualizedCaveatMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContextualizedCaveatMultiError) AllErrors() []error { return m }

// ContextualizedCaveatValidationError is the validation error returned by
// ContextualizedCaveat.Validate if the designated constraints aren't met.
type ContextualizedCaveatValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContextualizedCaveatValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContextualizedCaveatValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContextualizedCaveatValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContextualizedCaveatValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContextualizedCaveatValidationError) ErrorName() string {
	return "ContextualizedCaveatValidationError"
}

// Error satisfies the builtin error interface
func (e ContextualizedCaveatValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContextualizedCaveat.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContextualizedCaveatValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContextualizedCaveatValidationError{}

var _ContextualizedCaveat_CaveatName_Pattern = regexp.MustCompile("^(([a-zA-Z0-9_][a-zA-Z0-9/_|-]{0,127})|\\*)$")

// Validate checks the field values on CaveatDefinition with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CaveatDefinition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaveatDefinition with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CaveatDefinitionMultiError, or nil if none found.
func (m *CaveatDefinition) ValidateAll() error {
	return m.validate(true)
}

func (m *CaveatDefinition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetName()) > 128 {
		err := CaveatDefinitionValidationError{
			field:  "Name",
			reason: "value length must be at most 128 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CaveatDefinition_Name_Pattern.MatchString(m.GetName()) {
		err := CaveatDefinitionValidationError{
			field:  "Name",
			reason: "value does not match regex pattern \"^(([a-zA-Z0-9_][a-zA-Z0-9/_|-]{0,127})|\\\\*)$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetSerializedExpression()); l < 0 || l > 4096 {
		err := CaveatDefinitionValidationError{
			field:  "SerializedExpression",
			reason: "value length must be between 0 and 4096 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetParameterTypes()); l < 1 || l > 20 {
		err := CaveatDefinitionValidationError{
			field:  "ParameterTypes",
			reason: "value must contain between 1 and 20 pairs, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	{
		sorted_keys := make([]string, len(m.GetParameterTypes()))
		i := 0
		for key := range m.GetParameterTypes() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetParameterTypes()[key]
			_ = val

			// no validation rules for ParameterTypes[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, CaveatDefinitionValidationError{
							field:  fmt.Sprintf("ParameterTypes[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, CaveatDefinitionValidationError{
							field:  fmt.Sprintf("ParameterTypes[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return CaveatDefinitionValidationError{
						field:  fmt.Sprintf("ParameterTypes[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaveatDefinitionValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaveatDefinitionValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaveatDefinitionValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSourcePosition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaveatDefinitionValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaveatDefinitionValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourcePosition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaveatDefinitionValidationError{
				field:  "SourcePosition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CaveatDefinitionMultiError(errors)
	}

	return nil
}

// CaveatDefinitionMultiError is an error wrapping multiple validation errors
// returned by CaveatDefinition.ValidateAll() if the designated constraints
// aren't met.
type CaveatDefinitionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaveatDefinitionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaveatDefinitionMultiError) AllErrors() []error { return m }

// CaveatDefinitionValidationError is the validation error returned by
// CaveatDefinition.Validate if the designated constraints aren't met.
type CaveatDefinitionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaveatDefinitionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaveatDefinitionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaveatDefinitionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaveatDefinitionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaveatDefinitionValidationError) ErrorName() string { return "CaveatDefinitionValidationError" }

// Error satisfies the builtin error interface
func (e CaveatDefinitionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaveatDefinition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaveatDefinitionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaveatDefinitionValidationError{}

var _CaveatDefinition_Name_Pattern = regexp.MustCompile("^(([a-zA-Z0-9_][a-zA-Z0-9/_|-]{0,127})|\\*)$")

// Validate checks the field values on CaveatTypeReference with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CaveatTypeReference) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaveatTypeReference with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CaveatTypeReferenceMultiError, or nil if none found.
func (m *CaveatTypeReference) ValidateAll() error {
	return m.validate(true)
}

func (m *CaveatTypeReference) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TypeName

	if len(m.GetChildTypes()) > 1 {
		err := CaveatTypeReferenceValidationError{
			field:  "ChildTypes",
			reason: "value must contain no more than 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetChildTypes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CaveatTypeReferenceValidationError{
						field:  fmt.Sprintf("ChildTypes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CaveatTypeReferenceValidationError{
						field:  fmt.Sprintf("ChildTypes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CaveatTypeReferenceValidationError{
					field:  fmt.Sprintf("ChildTypes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CaveatTypeReferenceMultiError(errors)
	}

	return nil
}

// CaveatTypeReferenceMultiError is an error wrapping multiple validation
// errors returned by CaveatTypeReference.ValidateAll() if the designated
// constraints aren't met.
type CaveatTypeReferenceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaveatTypeReferenceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaveatTypeReferenceMultiError) AllErrors() []error { return m }

// CaveatTypeReferenceValidationError is the validation error returned by
// CaveatTypeReference.Validate if the designated constraints aren't met.
type CaveatTypeReferenceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaveatTypeReferenceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaveatTypeReferenceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaveatTypeReferenceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaveatTypeReferenceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaveatTypeReferenceValidationError) ErrorName() string {
	return "CaveatTypeReferenceValidationError"
}

// Error satisfies the builtin error interface
func (e CaveatTypeReferenceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaveatTypeReference.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaveatTypeReferenceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaveatTypeReferenceValidationError{}

// Validate checks the field values on ObjectAndRelation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ObjectAndRelation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ObjectAndRelation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ObjectAndRelationMultiError, or nil if none found.
func (m *ObjectAndRelation) ValidateAll() error {
	return m.validate(true)
}

func (m *ObjectAndRelation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetNamespace()) > 128 {
		err := ObjectAndRelationValidationError{
			field:  "Namespace",
			reason: "value length must be at most 128 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ObjectAndRelation_Namespace_Pattern.MatchString(m.GetNamespace()) {
		err := ObjectAndRelationValidationError{
			field:  "Namespace",
			reason: "value does not match regex pattern \"^([a-z][a-z0-9_]{1,61}[a-z0-9]/)*[a-z][a-z0-9_]{1,62}[a-z0-9]$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetObjectId()) > 1024 {
		err := ObjectAndRelationValidationError{
			field:  "ObjectId",
			reason: "value length must be at most 1024 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ObjectAndRelation_ObjectId_Pattern.MatchString(m.GetObjectId()) {
		err := ObjectAndRelationValidationError{
			field:  "ObjectId",
			reason: "value does not match regex pattern \"^(([a-zA-Z0-9/_|\\\\-=+]{1,})|\\\\*)$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetRelation()) > 64 {
		err := ObjectAndRelationValidationError{
			field:  "Relation",
			reason: "value length must be at most 64 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ObjectAndRelation_Relation_Pattern.MatchString(m.GetRelation()) {
		err := ObjectAndRelationValidationError{
			field:  "Relation",
			reason: "value does not match regex pattern \"^(\\\\.\\\\.\\\\.|[a-z][a-z0-9_]{1,62}[a-z0-9])$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ObjectAndRelationMultiError(errors)
	}

	return nil
}

// ObjectAndRelationMultiError is an error wrapping multiple validation errors
// returned by ObjectAndRelation.ValidateAll() if the designated constraints
// aren't met.
type ObjectAndRelationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ObjectAndRelationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ObjectAndRelationMultiError) AllErrors() []error { return m }

// ObjectAndRelationValidationError is the validation error returned by
// ObjectAndRelation.Validate if the designated constraints aren't met.
type ObjectAndRelationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ObjectAndRelationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ObjectAndRelationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ObjectAndRelationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ObjectAndRelationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ObjectAndRelationValidationError) ErrorName() string {
	return "ObjectAndRelationValidationError"
}

// Error satisfies the builtin error interface
func (e ObjectAndRelationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sObjectAndRelation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ObjectAndRelationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ObjectAndRelationValidationError{}

var _ObjectAndRelation_Namespace_Pattern = regexp.MustCompile("^([a-z][a-z0-9_]{1,61}[a-z0-9]/)*[a-z][a-z0-9_]{1,62}[a-z0-9]$")

var _ObjectAndRelation_ObjectId_Pattern = regexp.MustCompile("^(([a-zA-Z0-9/_|\\-=+]{1,})|\\*)$")

var _ObjectAndRelation_Relation_Pattern = regexp.MustCompile("^(\\.\\.\\.|[a-z][a-z0-9_]{1,62}[a-z0-9])$")

// Validate checks the field values on RelationReference with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RelationReference) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RelationReference with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RelationReferenceMultiError, or nil if none found.
func (m *RelationReference) ValidateAll() error {
	return m.validate(true)
}

func (m *RelationReference) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetNamespace()) > 128 {
		err := RelationReferenceValidationError{
			field:  "Namespace",
			reason: "value length must be at most 128 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_RelationReference_Namespace_Pattern.MatchString(m.GetNamespace()) {
		err := RelationReferenceValidationError{
			field:  "Namespace",
			reason: "value does not match regex pattern \"^([a-z][a-z0-9_]{1,61}[a-z0-9]/)*[a-z][a-z0-9_]{1,62}[a-z0-9]$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetRelation()) > 64 {
		err := RelationReferenceValidationError{
			field:  "Relation",
			reason: "value length must be at most 64 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_RelationReference_Relation_Pattern.MatchString(m.GetRelation()) {
		err := RelationReferenceValidationError{
			field:  "Relation",
			reason: "value does not match regex pattern \"^(\\\\.\\\\.\\\\.|[a-z][a-z0-9_]{1,62}[a-z0-9])$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RelationReferenceMultiError(errors)
	}

	return nil
}

// RelationReferenceMultiError is an error wrapping multiple validation errors
// returned by RelationReference.ValidateAll() if the designated constraints
// aren't met.
type RelationReferenceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RelationReferenceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RelationReferenceMultiError) AllErrors() []error { return m }

// RelationReferenceValidationError is the validation error returned by
// RelationReference.Validate if the designated constraints aren't met.
type RelationReferenceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RelationReferenceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RelationReferenceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RelationReferenceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RelationReferenceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RelationReferenceValidationError) ErrorName() string {
	return "RelationReferenceValidationError"
}

// Error satisfies the builtin error interface
func (e RelationReferenceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRelationReference.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RelationReferenceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RelationReferenceValidationError{}

var _RelationReference_Namespace_Pattern = regexp.MustCompile("^([a-z][a-z0-9_]{1,61}[a-z0-9]/)*[a-z][a-z0-9_]{1,62}[a-z0-9]$")

var _RelationReference_Relation_Pattern = regexp.MustCompile("^(\\.\\.\\.|[a-z][a-z0-9_]{1,62}[a-z0-9])$")

// Validate checks the field values on Zookie with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Zookie) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Zookie with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ZookieMultiError, or nil if none found.
func (m *Zookie) ValidateAll() error {
	return m.validate(true)
}

func (m *Zookie) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetToken()) < 1 {
		err := ZookieValidationError{
			field:  "Token",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ZookieMultiError(errors)
	}

	return nil
}

// ZookieMultiError is an error wrapping multiple validation errors returned by
// Zookie.ValidateAll() if the designated constraints aren't met.
type ZookieMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ZookieMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ZookieMultiError) AllErrors() []error { return m }

// ZookieValidationError is the validation error returned by Zookie.Validate if
// the designated constraints aren't met.
type ZookieValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ZookieValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ZookieValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ZookieValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ZookieValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ZookieValidationError) ErrorName() string { return "ZookieValidationError" }

// Error satisfies the builtin error interface
func (e ZookieValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sZookie.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ZookieValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ZookieValidationError{}

// Validate checks the field values on RelationTupleUpdate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RelationTupleUpdate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RelationTupleUpdate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RelationTupleUpdateMultiError, or nil if none found.
func (m *RelationTupleUpdate) ValidateAll() error {
	return m.validate(true)
}

func (m *RelationTupleUpdate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := RelationTupleUpdate_Operation_name[int32(m.GetOperation())]; !ok {
		err := RelationTupleUpdateValidationError{
			field:  "Operation",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTuple() == nil {
		err := RelationTupleUpdateValidationError{
			field:  "Tuple",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTuple()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RelationTupleUpdateValidationError{
					field:  "Tuple",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RelationTupleUpdateValidationError{
					field:  "Tuple",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTuple()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RelationTupleUpdateValidationError{
				field:  "Tuple",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RelationTupleUpdateMultiError(errors)
	}

	return nil
}

// RelationTupleUpdateMultiError is an error wrapping multiple validation
// errors returned by RelationTupleUpdate.ValidateAll() if the designated
// constraints aren't met.
type RelationTupleUpdateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RelationTupleUpdateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RelationTupleUpdateMultiError) AllErrors() []error { return m }

// RelationTupleUpdateValidationError is the validation error returned by
// RelationTupleUpdate.Validate if the designated constraints aren't met.
type RelationTupleUpdateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RelationTupleUpdateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RelationTupleUpdateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RelationTupleUpdateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RelationTupleUpdateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RelationTupleUpdateValidationError) ErrorName() string {
	return "RelationTupleUpdateValidationError"
}

// Error satisfies the builtin error interface
func (e RelationTupleUpdateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRelationTupleUpdate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RelationTupleUpdateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RelationTupleUpdateValidationError{}

// Validate checks the field values on RelationTupleTreeNode with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RelationTupleTreeNode) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RelationTupleTreeNode with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RelationTupleTreeNodeMultiError, or nil if none found.
func (m *RelationTupleTreeNode) ValidateAll() error {
	return m.validate(true)
}

func (m *RelationTupleTreeNode) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetExpanded()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RelationTupleTreeNodeValidationError{
					field:  "Expanded",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RelationTupleTreeNodeValidationError{
					field:  "Expanded",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpanded()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RelationTupleTreeNodeValidationError{
				field:  "Expanded",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCaveatExpression()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RelationTupleTreeNodeValidationError{
					field:  "CaveatExpression",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RelationTupleTreeNodeValidationError{
					field:  "CaveatExpression",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCaveatExpression()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RelationTupleTreeNodeValidationError{
				field:  "CaveatExpression",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.NodeType.(type) {
	case *RelationTupleTreeNode_IntermediateNode:
		if v == nil {
			err := RelationTupleTreeNodeValidationError{
				field:  "NodeType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetIntermediateNode()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RelationTupleTreeNodeValidationError{
						field:  "IntermediateNode",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RelationTupleTreeNodeValidationError{
						field:  "IntermediateNode",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIntermediateNode()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RelationTupleTreeNodeValidationError{
					field:  "IntermediateNode",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RelationTupleTreeNode_LeafNode:
		if v == nil {
			err := RelationTupleTreeNodeValidationError{
				field:  "NodeType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLeafNode()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RelationTupleTreeNodeValidationError{
						field:  "LeafNode",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RelationTupleTreeNodeValidationError{
						field:  "LeafNode",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLeafNode()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RelationTupleTreeNodeValidationError{
					field:  "LeafNode",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RelationTupleTreeNodeMultiError(errors)
	}

	return nil
}

// RelationTupleTreeNodeMultiError is an error wrapping multiple validation
// errors returned by RelationTupleTreeNode.ValidateAll() if the designated
// constraints aren't met.
type RelationTupleTreeNodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RelationTupleTreeNodeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RelationTupleTreeNodeMultiError) AllErrors() []error { return m }

// RelationTupleTreeNodeValidationError is the validation error returned by
// RelationTupleTreeNode.Validate if the designated constraints aren't met.
type RelationTupleTreeNodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RelationTupleTreeNodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RelationTupleTreeNodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RelationTupleTreeNodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RelationTupleTreeNodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RelationTupleTreeNodeValidationError) ErrorName() string {
	return "RelationTupleTreeNodeValidationError"
}

// Error satisfies the builtin error interface
func (e RelationTupleTreeNodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRelationTupleTreeNode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RelationTupleTreeNodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RelationTupleTreeNodeValidationError{}

// Validate checks the field values on SetOperationUserset with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetOperationUserset) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetOperationUserset with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetOperationUsersetMultiError, or nil if none found.
func (m *SetOperationUserset) ValidateAll() error {
	return m.validate(true)
}

func (m *SetOperationUserset) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Operation

	for idx, item := range m.GetChildNodes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetOperationUsersetValidationError{
						field:  fmt.Sprintf("ChildNodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetOperationUsersetValidationError{
						field:  fmt.Sprintf("ChildNodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetOperationUsersetValidationError{
					field:  fmt.Sprintf("ChildNodes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SetOperationUsersetMultiError(errors)
	}

	return nil
}

// SetOperationUsersetMultiError is an error wrapping multiple validation
// errors returned by SetOperationUserset.ValidateAll() if the designated
// constraints aren't met.
type SetOperationUsersetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetOperationUsersetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetOperationUsersetMultiError) AllErrors() []error { return m }

// SetOperationUsersetValidationError is the validation error returned by
// SetOperationUserset.Validate if the designated constraints aren't met.
type SetOperationUsersetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetOperationUsersetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetOperationUsersetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetOperationUsersetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetOperationUsersetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetOperationUsersetValidationError) ErrorName() string {
	return "SetOperationUsersetValidationError"
}

// Error satisfies the builtin error interface
func (e SetOperationUsersetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetOperationUserset.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetOperationUsersetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetOperationUsersetValidationError{}

// Validate checks the field values on DirectSubject with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DirectSubject) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectSubject with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DirectSubjectMultiError, or
// nil if none found.
func (m *DirectSubject) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectSubject) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSubject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DirectSubjectValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DirectSubjectValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DirectSubjectValidationError{
				field:  "Subject",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCaveatExpression()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DirectSubjectValidationError{
					field:  "CaveatExpression",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DirectSubjectValidationError{
					field:  "CaveatExpression",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCaveatExpression()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DirectSubjectValidationError{
				field:  "CaveatExpression",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DirectSubjectMultiError(errors)
	}

	return nil
}

// DirectSubjectMultiError is an error wrapping multiple validation errors
// returned by DirectSubject.ValidateAll() if the designated constraints
// aren't met.
type DirectSubjectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectSubjectMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectSubjectMultiError) AllErrors() []error { return m }

// DirectSubjectValidationError is the validation error returned by
// DirectSubject.Validate if the designated constraints aren't met.
type DirectSubjectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectSubjectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectSubjectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectSubjectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectSubjectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectSubjectValidationError) ErrorName() string { return "DirectSubjectValidationError" }

// Error satisfies the builtin error interface
func (e DirectSubjectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectSubject.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectSubjectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectSubjectValidationError{}

// Validate checks the field values on DirectSubjects with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DirectSubjects) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectSubjects with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DirectSubjectsMultiError,
// or nil if none found.
func (m *DirectSubjects) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectSubjects) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetSubjects() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DirectSubjectsValidationError{
						field:  fmt.Sprintf("Subjects[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DirectSubjectsValidationError{
						field:  fmt.Sprintf("Subjects[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DirectSubjectsValidationError{
					field:  fmt.Sprintf("Subjects[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DirectSubjectsMultiError(errors)
	}

	return nil
}

// DirectSubjectsMultiError is an error wrapping multiple validation errors
// returned by DirectSubjects.ValidateAll() if the designated constraints
// aren't met.
type DirectSubjectsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectSubjectsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectSubjectsMultiError) AllErrors() []error { return m }

// DirectSubjectsValidationError is the validation error returned by
// DirectSubjects.Validate if the designated constraints aren't met.
type DirectSubjectsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectSubjectsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectSubjectsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectSubjectsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectSubjectsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectSubjectsValidationError) ErrorName() string { return "DirectSubjectsValidationError" }

// Error satisfies the builtin error interface
func (e DirectSubjectsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectSubjects.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectSubjectsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectSubjectsValidationError{}

// Validate checks the field values on Metadata with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Metadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Metadata with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MetadataMultiError, or nil
// if none found.
func (m *Metadata) ValidateAll() error {
	return m.validate(true)
}

func (m *Metadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetMetadataMessage()) < 1 {
		err := MetadataValidationError{
			field:  "MetadataMessage",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetMetadataMessage() {
		_, _ = idx, item

		if item == nil {
			err := MetadataValidationError{
				field:  fmt.Sprintf("MetadataMessage[%v]", idx),
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if a := item; a != nil {

			if _, ok := _Metadata_MetadataMessage_InLookup[a.GetTypeUrl()]; !ok {
				err := MetadataValidationError{
					field:  fmt.Sprintf("MetadataMessage[%v]", idx),
					reason: "type URL must be in list [type.googleapis.com/impl.v1.DocComment type.googleapis.com/impl.v1.RelationMetadata]",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}

	}

	if len(errors) > 0 {
		return MetadataMultiError(errors)
	}

	return nil
}

// MetadataMultiError is an error wrapping multiple validation errors returned
// by Metadata.ValidateAll() if the designated constraints aren't met.
type MetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MetadataMultiError) AllErrors() []error { return m }

// MetadataValidationError is the validation error returned by
// Metadata.Validate if the designated constraints aren't met.
type MetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MetadataValidationError) ErrorName() string { return "MetadataValidationError" }

// Error satisfies the builtin error interface
func (e MetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MetadataValidationError{}

var _Metadata_MetadataMessage_InLookup = map[string]struct{}{
	"type.googleapis.com/impl.v1.DocComment":       {},
	"type.googleapis.com/impl.v1.RelationMetadata": {},
}

// Validate checks the field values on NamespaceDefinition with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NamespaceDefinition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NamespaceDefinition with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NamespaceDefinitionMultiError, or nil if none found.
func (m *NamespaceDefinition) ValidateAll() error {
	return m.validate(true)
}

func (m *NamespaceDefinition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetName()) > 128 {
		err := NamespaceDefinitionValidationError{
			field:  "Name",
			reason: "value length must be at most 128 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_NamespaceDefinition_Name_Pattern.MatchString(m.GetName()) {
		err := NamespaceDefinitionValidationError{
			field:  "Name",
			reason: "value does not match regex pattern \"^([a-z][a-z0-9_]{1,62}[a-z0-9]/)*[a-z][a-z0-9_]{1,62}[a-z0-9]$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetRelation() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NamespaceDefinitionValidationError{
						field:  fmt.Sprintf("Relation[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NamespaceDefinitionValidationError{
						field:  fmt.Sprintf("Relation[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NamespaceDefinitionValidationError{
					field:  fmt.Sprintf("Relation[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NamespaceDefinitionValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NamespaceDefinitionValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NamespaceDefinitionValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSourcePosition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NamespaceDefinitionValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NamespaceDefinitionValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourcePosition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NamespaceDefinitionValidationError{
				field:  "SourcePosition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NamespaceDefinitionMultiError(errors)
	}

	return nil
}

// NamespaceDefinitionMultiError is an error wrapping multiple validation
// errors returned by NamespaceDefinition.ValidateAll() if the designated
// constraints aren't met.
type NamespaceDefinitionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NamespaceDefinitionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NamespaceDefinitionMultiError) AllErrors() []error { return m }

// NamespaceDefinitionValidationError is the validation error returned by
// NamespaceDefinition.Validate if the designated constraints aren't met.
type NamespaceDefinitionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NamespaceDefinitionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NamespaceDefinitionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NamespaceDefinitionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NamespaceDefinitionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NamespaceDefinitionValidationError) ErrorName() string {
	return "NamespaceDefinitionValidationError"
}

// Error satisfies the builtin error interface
func (e NamespaceDefinitionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNamespaceDefinition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NamespaceDefinitionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NamespaceDefinitionValidationError{}

var _NamespaceDefinition_Name_Pattern = regexp.MustCompile("^([a-z][a-z0-9_]{1,62}[a-z0-9]/)*[a-z][a-z0-9_]{1,62}[a-z0-9]$")

// Validate checks the field values on Relation with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Relation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Relation with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RelationMultiError, or nil
// if none found.
func (m *Relation) ValidateAll() error {
	return m.validate(true)
}

func (m *Relation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetName()) > 64 {
		err := RelationValidationError{
			field:  "Name",
			reason: "value length must be at most 64 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_Relation_Name_Pattern.MatchString(m.GetName()) {
		err := RelationValidationError{
			field:  "Name",
			reason: "value does not match regex pattern \"^[a-z][a-z0-9_]{1,62}[a-z0-9]$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetUsersetRewrite()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RelationValidationError{
					field:  "UsersetRewrite",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RelationValidationError{
					field:  "UsersetRewrite",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUsersetRewrite()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RelationValidationError{
				field:  "UsersetRewrite",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTypeInformation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RelationValidationError{
					field:  "TypeInformation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RelationValidationError{
					field:  "TypeInformation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTypeInformation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RelationValidationError{
				field:  "TypeInformation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RelationValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RelationValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RelationValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSourcePosition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RelationValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RelationValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourcePosition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RelationValidationError{
				field:  "SourcePosition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AliasingRelation

	// no validation rules for CanonicalCacheKey

	if len(errors) > 0 {
		return RelationMultiError(errors)
	}

	return nil
}

// RelationMultiError is an error wrapping multiple validation errors returned
// by Relation.ValidateAll() if the designated constraints aren't met.
type RelationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RelationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RelationMultiError) AllErrors() []error { return m }

// RelationValidationError is the validation error returned by
// Relation.Validate if the designated constraints aren't met.
type RelationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RelationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RelationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RelationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RelationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RelationValidationError) ErrorName() string { return "RelationValidationError" }

// Error satisfies the builtin error interface
func (e RelationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRelation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RelationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RelationValidationError{}

var _Relation_Name_Pattern = regexp.MustCompile("^[a-z][a-z0-9_]{1,62}[a-z0-9]$")

// Validate checks the field values on ReachabilityGraph with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReachabilityGraph) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReachabilityGraph with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReachabilityGraphMultiError, or nil if none found.
func (m *ReachabilityGraph) ValidateAll() error {
	return m.validate(true)
}

func (m *ReachabilityGraph) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetEntrypointsBySubjectType()))
		i := 0
		for key := range m.GetEntrypointsBySubjectType() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetEntrypointsBySubjectType()[key]
			_ = val

			// no validation rules for EntrypointsBySubjectType[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ReachabilityGraphValidationError{
							field:  fmt.Sprintf("EntrypointsBySubjectType[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ReachabilityGraphValidationError{
							field:  fmt.Sprintf("EntrypointsBySubjectType[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ReachabilityGraphValidationError{
						field:  fmt.Sprintf("EntrypointsBySubjectType[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	{
		sorted_keys := make([]string, len(m.GetEntrypointsBySubjectRelation()))
		i := 0
		for key := range m.GetEntrypointsBySubjectRelation() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetEntrypointsBySubjectRelation()[key]
			_ = val

			// no validation rules for EntrypointsBySubjectRelation[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ReachabilityGraphValidationError{
							field:  fmt.Sprintf("EntrypointsBySubjectRelation[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ReachabilityGraphValidationError{
							field:  fmt.Sprintf("EntrypointsBySubjectRelation[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ReachabilityGraphValidationError{
						field:  fmt.Sprintf("EntrypointsBySubjectRelation[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return ReachabilityGraphMultiError(errors)
	}

	return nil
}

// ReachabilityGraphMultiError is an error wrapping multiple validation errors
// returned by ReachabilityGraph.ValidateAll() if the designated constraints
// aren't met.
type ReachabilityGraphMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReachabilityGraphMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReachabilityGraphMultiError) AllErrors() []error { return m }

// ReachabilityGraphValidationError is the validation error returned by
// ReachabilityGraph.Validate if the designated constraints aren't met.
type ReachabilityGraphValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReachabilityGraphValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReachabilityGraphValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReachabilityGraphValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReachabilityGraphValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReachabilityGraphValidationError) ErrorName() string {
	return "ReachabilityGraphValidationError"
}

// Error satisfies the builtin error interface
func (e ReachabilityGraphValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReachabilityGraph.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReachabilityGraphValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReachabilityGraphValidationError{}

// Validate checks the field values on ReachabilityEntrypoints with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReachabilityEntrypoints) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReachabilityEntrypoints with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReachabilityEntrypointsMultiError, or nil if none found.
func (m *ReachabilityEntrypoints) ValidateAll() error {
	return m.validate(true)
}

func (m *ReachabilityEntrypoints) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetEntrypoints() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReachabilityEntrypointsValidationError{
						field:  fmt.Sprintf("Entrypoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReachabilityEntrypointsValidationError{
						field:  fmt.Sprintf("Entrypoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReachabilityEntrypointsValidationError{
					field:  fmt.Sprintf("Entrypoints[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SubjectType

	if all {
		switch v := interface{}(m.GetSubjectRelation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReachabilityEntrypointsValidationError{
					field:  "SubjectRelation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReachabilityEntrypointsValidationError{
					field:  "SubjectRelation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubjectRelation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReachabilityEntrypointsValidationError{
				field:  "SubjectRelation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReachabilityEntrypointsMultiError(errors)
	}

	return nil
}

// ReachabilityEntrypointsMultiError is an error wrapping multiple validation
// errors returned by ReachabilityEntrypoints.ValidateAll() if the designated
// constraints aren't met.
type ReachabilityEntrypointsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReachabilityEntrypointsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReachabilityEntrypointsMultiError) AllErrors() []error { return m }

// ReachabilityEntrypointsValidationError is the validation error returned by
// ReachabilityEntrypoints.Validate if the designated constraints aren't met.
type ReachabilityEntrypointsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReachabilityEntrypointsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReachabilityEntrypointsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReachabilityEntrypointsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReachabilityEntrypointsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReachabilityEntrypointsValidationError) ErrorName() string {
	return "ReachabilityEntrypointsValidationError"
}

// Error satisfies the builtin error interface
func (e ReachabilityEntrypointsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReachabilityEntrypoints.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReachabilityEntrypointsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReachabilityEntrypointsValidationError{}

// Validate checks the field values on ReachabilityEntrypoint with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReachabilityEntrypoint) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReachabilityEntrypoint with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReachabilityEntrypointMultiError, or nil if none found.
func (m *ReachabilityEntrypoint) ValidateAll() error {
	return m.validate(true)
}

func (m *ReachabilityEntrypoint) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Kind

	if all {
		switch v := interface{}(m.GetTargetRelation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReachabilityEntrypointValidationError{
					field:  "TargetRelation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReachabilityEntrypointValidationError{
					field:  "TargetRelation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTargetRelation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReachabilityEntrypointValidationError{
				field:  "TargetRelation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ResultStatus

	// no validation rules for TuplesetRelation

	// no validation rules for ComputedUsersetRelation

	if len(errors) > 0 {
		return ReachabilityEntrypointMultiError(errors)
	}

	return nil
}

// ReachabilityEntrypointMultiError is an error wrapping multiple validation
// errors returned by ReachabilityEntrypoint.ValidateAll() if the designated
// constraints aren't met.
type ReachabilityEntrypointMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReachabilityEntrypointMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReachabilityEntrypointMultiError) AllErrors() []error { return m }

// ReachabilityEntrypointValidationError is the validation error returned by
// ReachabilityEntrypoint.Validate if the designated constraints aren't met.
type ReachabilityEntrypointValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReachabilityEntrypointValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReachabilityEntrypointValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReachabilityEntrypointValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReachabilityEntrypointValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReachabilityEntrypointValidationError) ErrorName() string {
	return "ReachabilityEntrypointValidationError"
}

// Error satisfies the builtin error interface
func (e ReachabilityEntrypointValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReachabilityEntrypoint.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReachabilityEntrypointValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReachabilityEntrypointValidationError{}

// Validate checks the field values on TypeInformation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TypeInformation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TypeInformation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TypeInformationMultiError, or nil if none found.
func (m *TypeInformation) ValidateAll() error {
	return m.validate(true)
}

func (m *TypeInformation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAllowedDirectRelations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TypeInformationValidationError{
						field:  fmt.Sprintf("AllowedDirectRelations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TypeInformationValidationError{
						field:  fmt.Sprintf("AllowedDirectRelations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TypeInformationValidationError{
					field:  fmt.Sprintf("AllowedDirectRelations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TypeInformationMultiError(errors)
	}

	return nil
}

// TypeInformationMultiError is an error wrapping multiple validation errors
// returned by TypeInformation.ValidateAll() if the designated constraints
// aren't met.
type TypeInformationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TypeInformationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TypeInformationMultiError) AllErrors() []error { return m }

// TypeInformationValidationError is the validation error returned by
// TypeInformation.Validate if the designated constraints aren't met.
type TypeInformationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TypeInformationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TypeInformationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TypeInformationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TypeInformationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TypeInformationValidationError) ErrorName() string { return "TypeInformationValidationError" }

// Error satisfies the builtin error interface
func (e TypeInformationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTypeInformation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TypeInformationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TypeInformationValidationError{}

// Validate checks the field values on AllowedRelation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AllowedRelation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AllowedRelation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AllowedRelationMultiError, or nil if none found.
func (m *AllowedRelation) ValidateAll() error {
	return m.validate(true)
}

func (m *AllowedRelation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetNamespace()) > 128 {
		err := AllowedRelationValidationError{
			field:  "Namespace",
			reason: "value length must be at most 128 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_AllowedRelation_Namespace_Pattern.MatchString(m.GetNamespace()) {
		err := AllowedRelationValidationError{
			field:  "Namespace",
			reason: "value does not match regex pattern \"^([a-z][a-z0-9_]{1,61}[a-z0-9]/)*[a-z][a-z0-9_]{1,62}[a-z0-9]$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSourcePosition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AllowedRelationValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AllowedRelationValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourcePosition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AllowedRelationValidationError{
				field:  "SourcePosition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRequiredCaveat()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AllowedRelationValidationError{
					field:  "RequiredCaveat",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AllowedRelationValidationError{
					field:  "RequiredCaveat",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequiredCaveat()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AllowedRelationValidationError{
				field:  "RequiredCaveat",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRequiredExpiration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AllowedRelationValidationError{
					field:  "RequiredExpiration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AllowedRelationValidationError{
					field:  "RequiredExpiration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequiredExpiration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AllowedRelationValidationError{
				field:  "RequiredExpiration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.RelationOrWildcard.(type) {
	case *AllowedRelation_Relation:
		if v == nil {
			err := AllowedRelationValidationError{
				field:  "RelationOrWildcard",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if len(m.GetRelation()) > 64 {
			err := AllowedRelationValidationError{
				field:  "Relation",
				reason: "value length must be at most 64 bytes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_AllowedRelation_Relation_Pattern.MatchString(m.GetRelation()) {
			err := AllowedRelationValidationError{
				field:  "Relation",
				reason: "value does not match regex pattern \"^(\\\\.\\\\.\\\\.|[a-z][a-z0-9_]{1,62}[a-z0-9])$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *AllowedRelation_PublicWildcard_:
		if v == nil {
			err := AllowedRelationValidationError{
				field:  "RelationOrWildcard",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPublicWildcard()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AllowedRelationValidationError{
						field:  "PublicWildcard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AllowedRelationValidationError{
						field:  "PublicWildcard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPublicWildcard()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AllowedRelationValidationError{
					field:  "PublicWildcard",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AllowedRelationMultiError(errors)
	}

	return nil
}

// AllowedRelationMultiError is an error wrapping multiple validation errors
// returned by AllowedRelation.ValidateAll() if the designated constraints
// aren't met.
type AllowedRelationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AllowedRelationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AllowedRelationMultiError) AllErrors() []error { return m }

// AllowedRelationValidationError is the validation error returned by
// AllowedRelation.Validate if the designated constraints aren't met.
type AllowedRelationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AllowedRelationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AllowedRelationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AllowedRelationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AllowedRelationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AllowedRelationValidationError) ErrorName() string { return "AllowedRelationValidationError" }

// Error satisfies the builtin error interface
func (e AllowedRelationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAllowedRelation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AllowedRelationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AllowedRelationValidationError{}

var _AllowedRelation_Namespace_Pattern = regexp.MustCompile("^([a-z][a-z0-9_]{1,61}[a-z0-9]/)*[a-z][a-z0-9_]{1,62}[a-z0-9]$")

var _AllowedRelation_Relation_Pattern = regexp.MustCompile("^(\\.\\.\\.|[a-z][a-z0-9_]{1,62}[a-z0-9])$")

// Validate checks the field values on ExpirationTrait with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ExpirationTrait) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExpirationTrait with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExpirationTraitMultiError, or nil if none found.
func (m *ExpirationTrait) ValidateAll() error {
	return m.validate(true)
}

func (m *ExpirationTrait) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ExpirationTraitMultiError(errors)
	}

	return nil
}

// ExpirationTraitMultiError is an error wrapping multiple validation errors
// returned by ExpirationTrait.ValidateAll() if the designated constraints
// aren't met.
type ExpirationTraitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExpirationTraitMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExpirationTraitMultiError) AllErrors() []error { return m }

// ExpirationTraitValidationError is the validation error returned by
// ExpirationTrait.Validate if the designated constraints aren't met.
type ExpirationTraitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExpirationTraitValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExpirationTraitValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExpirationTraitValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExpirationTraitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExpirationTraitValidationError) ErrorName() string { return "ExpirationTraitValidationError" }

// Error satisfies the builtin error interface
func (e ExpirationTraitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExpirationTrait.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExpirationTraitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExpirationTraitValidationError{}

// Validate checks the field values on AllowedCaveat with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AllowedCaveat) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AllowedCaveat with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AllowedCaveatMultiError, or
// nil if none found.
func (m *AllowedCaveat) ValidateAll() error {
	return m.validate(true)
}

func (m *AllowedCaveat) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaveatName

	if len(errors) > 0 {
		return AllowedCaveatMultiError(errors)
	}

	return nil
}

// AllowedCaveatMultiError is an error wrapping multiple validation errors
// returned by AllowedCaveat.ValidateAll() if the designated constraints
// aren't met.
type AllowedCaveatMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AllowedCaveatMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AllowedCaveatMultiError) AllErrors() []error { return m }

// AllowedCaveatValidationError is the validation error returned by
// AllowedCaveat.Validate if the designated constraints aren't met.
type AllowedCaveatValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AllowedCaveatValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AllowedCaveatValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AllowedCaveatValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AllowedCaveatValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AllowedCaveatValidationError) ErrorName() string { return "AllowedCaveatValidationError" }

// Error satisfies the builtin error interface
func (e AllowedCaveatValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAllowedCaveat.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AllowedCaveatValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AllowedCaveatValidationError{}

// Validate checks the field values on UsersetRewrite with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UsersetRewrite) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UsersetRewrite with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UsersetRewriteMultiError,
// or nil if none found.
func (m *UsersetRewrite) ValidateAll() error {
	return m.validate(true)
}

func (m *UsersetRewrite) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSourcePosition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UsersetRewriteValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UsersetRewriteValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourcePosition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UsersetRewriteValidationError{
				field:  "SourcePosition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	oneofRewriteOperationPresent := false
	switch v := m.RewriteOperation.(type) {
	case *UsersetRewrite_Union:
		if v == nil {
			err := UsersetRewriteValidationError{
				field:  "RewriteOperation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofRewriteOperationPresent = true

		if m.GetUnion() == nil {
			err := UsersetRewriteValidationError{
				field:  "Union",
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUnion()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UsersetRewriteValidationError{
						field:  "Union",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UsersetRewriteValidationError{
						field:  "Union",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUnion()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UsersetRewriteValidationError{
					field:  "Union",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UsersetRewrite_Intersection:
		if v == nil {
			err := UsersetRewriteValidationError{
				field:  "RewriteOperation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofRewriteOperationPresent = true

		if m.GetIntersection() == nil {
			err := UsersetRewriteValidationError{
				field:  "Intersection",
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetIntersection()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UsersetRewriteValidationError{
						field:  "Intersection",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UsersetRewriteValidationError{
						field:  "Intersection",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIntersection()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UsersetRewriteValidationError{
					field:  "Intersection",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UsersetRewrite_Exclusion:
		if v == nil {
			err := UsersetRewriteValidationError{
				field:  "RewriteOperation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofRewriteOperationPresent = true

		if m.GetExclusion() == nil {
			err := UsersetRewriteValidationError{
				field:  "Exclusion",
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExclusion()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UsersetRewriteValidationError{
						field:  "Exclusion",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UsersetRewriteValidationError{
						field:  "Exclusion",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExclusion()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UsersetRewriteValidationError{
					field:  "Exclusion",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	if !oneofRewriteOperationPresent {
		err := UsersetRewriteValidationError{
			field:  "RewriteOperation",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UsersetRewriteMultiError(errors)
	}

	return nil
}

// UsersetRewriteMultiError is an error wrapping multiple validation errors
// returned by UsersetRewrite.ValidateAll() if the designated constraints
// aren't met.
type UsersetRewriteMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UsersetRewriteMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UsersetRewriteMultiError) AllErrors() []error { return m }

// UsersetRewriteValidationError is the validation error returned by
// UsersetRewrite.Validate if the designated constraints aren't met.
type UsersetRewriteValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UsersetRewriteValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UsersetRewriteValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UsersetRewriteValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UsersetRewriteValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UsersetRewriteValidationError) ErrorName() string { return "UsersetRewriteValidationError" }

// Error satisfies the builtin error interface
func (e UsersetRewriteValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUsersetRewrite.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UsersetRewriteValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UsersetRewriteValidationError{}

// Validate checks the field values on SetOperation with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SetOperation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetOperation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SetOperationMultiError, or
// nil if none found.
func (m *SetOperation) ValidateAll() error {
	return m.validate(true)
}

func (m *SetOperation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetChild()) < 1 {
		err := SetOperationValidationError{
			field:  "Child",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetChild() {
		_, _ = idx, item

		if item == nil {
			err := SetOperationValidationError{
				field:  fmt.Sprintf("Child[%v]", idx),
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetOperationValidationError{
						field:  fmt.Sprintf("Child[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetOperationValidationError{
						field:  fmt.Sprintf("Child[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetOperationValidationError{
					field:  fmt.Sprintf("Child[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SetOperationMultiError(errors)
	}

	return nil
}

// SetOperationMultiError is an error wrapping multiple validation errors
// returned by SetOperation.ValidateAll() if the designated constraints aren't met.
type SetOperationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetOperationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetOperationMultiError) AllErrors() []error { return m }

// SetOperationValidationError is the validation error returned by
// SetOperation.Validate if the designated constraints aren't met.
type SetOperationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetOperationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetOperationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetOperationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetOperationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetOperationValidationError) ErrorName() string { return "SetOperationValidationError" }

// Error satisfies the builtin error interface
func (e SetOperationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetOperation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetOperationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetOperationValidationError{}

// Validate checks the field values on TupleToUserset with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TupleToUserset) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TupleToUserset with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TupleToUsersetMultiError,
// or nil if none found.
func (m *TupleToUserset) ValidateAll() error {
	return m.validate(true)
}

func (m *TupleToUserset) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetTupleset() == nil {
		err := TupleToUsersetValidationError{
			field:  "Tupleset",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTupleset()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TupleToUsersetValidationError{
					field:  "Tupleset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TupleToUsersetValidationError{
					field:  "Tupleset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTupleset()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TupleToUsersetValidationError{
				field:  "Tupleset",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetComputedUserset() == nil {
		err := TupleToUsersetValidationError{
			field:  "ComputedUserset",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetComputedUserset()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TupleToUsersetValidationError{
					field:  "ComputedUserset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TupleToUsersetValidationError{
					field:  "ComputedUserset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetComputedUserset()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TupleToUsersetValidationError{
				field:  "ComputedUserset",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSourcePosition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TupleToUsersetValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TupleToUsersetValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourcePosition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TupleToUsersetValidationError{
				field:  "SourcePosition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TupleToUsersetMultiError(errors)
	}

	return nil
}

// TupleToUsersetMultiError is an error wrapping multiple validation errors
// returned by TupleToUserset.ValidateAll() if the designated constraints
// aren't met.
type TupleToUsersetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TupleToUsersetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TupleToUsersetMultiError) AllErrors() []error { return m }

// TupleToUsersetValidationError is the validation error returned by
// TupleToUserset.Validate if the designated constraints aren't met.
type TupleToUsersetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TupleToUsersetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TupleToUsersetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TupleToUsersetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TupleToUsersetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TupleToUsersetValidationError) ErrorName() string { return "TupleToUsersetValidationError" }

// Error satisfies the builtin error interface
func (e TupleToUsersetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTupleToUserset.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TupleToUsersetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TupleToUsersetValidationError{}

// Validate checks the field values on FunctionedTupleToUserset with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FunctionedTupleToUserset) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FunctionedTupleToUserset with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FunctionedTupleToUsersetMultiError, or nil if none found.
func (m *FunctionedTupleToUserset) ValidateAll() error {
	return m.validate(true)
}

func (m *FunctionedTupleToUserset) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _FunctionedTupleToUserset_Function_NotInLookup[m.GetFunction()]; ok {
		err := FunctionedTupleToUsersetValidationError{
			field:  "Function",
			reason: "value must not be in list [FUNCTION_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := FunctionedTupleToUserset_Function_name[int32(m.GetFunction())]; !ok {
		err := FunctionedTupleToUsersetValidationError{
			field:  "Function",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTupleset() == nil {
		err := FunctionedTupleToUsersetValidationError{
			field:  "Tupleset",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTupleset()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FunctionedTupleToUsersetValidationError{
					field:  "Tupleset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FunctionedTupleToUsersetValidationError{
					field:  "Tupleset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTupleset()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FunctionedTupleToUsersetValidationError{
				field:  "Tupleset",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetComputedUserset() == nil {
		err := FunctionedTupleToUsersetValidationError{
			field:  "ComputedUserset",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetComputedUserset()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FunctionedTupleToUsersetValidationError{
					field:  "ComputedUserset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FunctionedTupleToUsersetValidationError{
					field:  "ComputedUserset",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetComputedUserset()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FunctionedTupleToUsersetValidationError{
				field:  "ComputedUserset",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSourcePosition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FunctionedTupleToUsersetValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FunctionedTupleToUsersetValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourcePosition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FunctionedTupleToUsersetValidationError{
				field:  "SourcePosition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FunctionedTupleToUsersetMultiError(errors)
	}

	return nil
}

// FunctionedTupleToUsersetMultiError is an error wrapping multiple validation
// errors returned by FunctionedTupleToUserset.ValidateAll() if the designated
// constraints aren't met.
type FunctionedTupleToUsersetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FunctionedTupleToUsersetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FunctionedTupleToUsersetMultiError) AllErrors() []error { return m }

// FunctionedTupleToUsersetValidationError is the validation error returned by
// FunctionedTupleToUserset.Validate if the designated constraints aren't met.
type FunctionedTupleToUsersetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FunctionedTupleToUsersetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FunctionedTupleToUsersetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FunctionedTupleToUsersetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FunctionedTupleToUsersetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FunctionedTupleToUsersetValidationError) ErrorName() string {
	return "FunctionedTupleToUsersetValidationError"
}

// Error satisfies the builtin error interface
func (e FunctionedTupleToUsersetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFunctionedTupleToUserset.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FunctionedTupleToUsersetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FunctionedTupleToUsersetValidationError{}

var _FunctionedTupleToUserset_Function_NotInLookup = map[FunctionedTupleToUserset_Function]struct{}{
	0: {},
}

// Validate checks the field values on ComputedUserset with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ComputedUserset) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ComputedUserset with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ComputedUsersetMultiError, or nil if none found.
func (m *ComputedUserset) ValidateAll() error {
	return m.validate(true)
}

func (m *ComputedUserset) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := ComputedUserset_Object_name[int32(m.GetObject())]; !ok {
		err := ComputedUsersetValidationError{
			field:  "Object",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetRelation()) > 64 {
		err := ComputedUsersetValidationError{
			field:  "Relation",
			reason: "value length must be at most 64 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ComputedUserset_Relation_Pattern.MatchString(m.GetRelation()) {
		err := ComputedUsersetValidationError{
			field:  "Relation",
			reason: "value does not match regex pattern \"^[a-z][a-z0-9_]{1,62}[a-z0-9]$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSourcePosition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComputedUsersetValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComputedUsersetValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourcePosition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComputedUsersetValidationError{
				field:  "SourcePosition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ComputedUsersetMultiError(errors)
	}

	return nil
}

// ComputedUsersetMultiError is an error wrapping multiple validation errors
// returned by ComputedUserset.ValidateAll() if the designated constraints
// aren't met.
type ComputedUsersetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ComputedUsersetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ComputedUsersetMultiError) AllErrors() []error { return m }

// ComputedUsersetValidationError is the validation error returned by
// ComputedUserset.Validate if the designated constraints aren't met.
type ComputedUsersetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ComputedUsersetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ComputedUsersetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ComputedUsersetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ComputedUsersetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ComputedUsersetValidationError) ErrorName() string { return "ComputedUsersetValidationError" }

// Error satisfies the builtin error interface
func (e ComputedUsersetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComputedUserset.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ComputedUsersetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ComputedUsersetValidationError{}

var _ComputedUserset_Relation_Pattern = regexp.MustCompile("^[a-z][a-z0-9_]{1,62}[a-z0-9]$")

// Validate checks the field values on SourcePosition with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SourcePosition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SourcePosition with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SourcePositionMultiError,
// or nil if none found.
func (m *SourcePosition) ValidateAll() error {
	return m.validate(true)
}

func (m *SourcePosition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ZeroIndexedLineNumber

	// no validation rules for ZeroIndexedColumnPosition

	if len(errors) > 0 {
		return SourcePositionMultiError(errors)
	}

	return nil
}

// SourcePositionMultiError is an error wrapping multiple validation errors
// returned by SourcePosition.ValidateAll() if the designated constraints
// aren't met.
type SourcePositionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SourcePositionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SourcePositionMultiError) AllErrors() []error { return m }

// SourcePositionValidationError is the validation error returned by
// SourcePosition.Validate if the designated constraints aren't met.
type SourcePositionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SourcePositionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SourcePositionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SourcePositionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SourcePositionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SourcePositionValidationError) ErrorName() string { return "SourcePositionValidationError" }

// Error satisfies the builtin error interface
func (e SourcePositionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSourcePosition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SourcePositionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SourcePositionValidationError{}

// Validate checks the field values on CaveatExpression with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CaveatExpression) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaveatExpression with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CaveatExpressionMultiError, or nil if none found.
func (m *CaveatExpression) ValidateAll() error {
	return m.validate(true)
}

func (m *CaveatExpression) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.OperationOrCaveat.(type) {
	case *CaveatExpression_Operation:
		if v == nil {
			err := CaveatExpressionValidationError{
				field:  "OperationOrCaveat",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOperation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CaveatExpressionValidationError{
						field:  "Operation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CaveatExpressionValidationError{
						field:  "Operation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOperation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CaveatExpressionValidationError{
					field:  "Operation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CaveatExpression_Caveat:
		if v == nil {
			err := CaveatExpressionValidationError{
				field:  "OperationOrCaveat",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCaveat()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CaveatExpressionValidationError{
						field:  "Caveat",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CaveatExpressionValidationError{
						field:  "Caveat",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCaveat()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CaveatExpressionValidationError{
					field:  "Caveat",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CaveatExpressionMultiError(errors)
	}

	return nil
}

// CaveatExpressionMultiError is an error wrapping multiple validation errors
// returned by CaveatExpression.ValidateAll() if the designated constraints
// aren't met.
type CaveatExpressionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaveatExpressionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaveatExpressionMultiError) AllErrors() []error { return m }

// CaveatExpressionValidationError is the validation error returned by
// CaveatExpression.Validate if the designated constraints aren't met.
type CaveatExpressionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaveatExpressionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaveatExpressionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaveatExpressionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaveatExpressionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaveatExpressionValidationError) ErrorName() string { return "CaveatExpressionValidationError" }

// Error satisfies the builtin error interface
func (e CaveatExpressionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaveatExpression.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaveatExpressionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaveatExpressionValidationError{}

// Validate checks the field values on CaveatOperation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CaveatOperation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaveatOperation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CaveatOperationMultiError, or nil if none found.
func (m *CaveatOperation) ValidateAll() error {
	return m.validate(true)
}

func (m *CaveatOperation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Op

	for idx, item := range m.GetChildren() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CaveatOperationValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CaveatOperationValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CaveatOperationValidationError{
					field:  fmt.Sprintf("Children[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CaveatOperationMultiError(errors)
	}

	return nil
}

// CaveatOperationMultiError is an error wrapping multiple validation errors
// returned by CaveatOperation.ValidateAll() if the designated constraints
// aren't met.
type CaveatOperationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaveatOperationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaveatOperationMultiError) AllErrors() []error { return m }

// CaveatOperationValidationError is the validation error returned by
// CaveatOperation.Validate if the designated constraints aren't met.
type CaveatOperationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaveatOperationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaveatOperationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaveatOperationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaveatOperationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaveatOperationValidationError) ErrorName() string { return "CaveatOperationValidationError" }

// Error satisfies the builtin error interface
func (e CaveatOperationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaveatOperation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaveatOperationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaveatOperationValidationError{}

// Validate checks the field values on RelationshipFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RelationshipFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RelationshipFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RelationshipFilterMultiError, or nil if none found.
func (m *RelationshipFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *RelationshipFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetResourceType()) > 128 {
		err := RelationshipFilterValidationError{
			field:  "ResourceType",
			reason: "value length must be at most 128 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_RelationshipFilter_ResourceType_Pattern.MatchString(m.GetResourceType()) {
		err := RelationshipFilterValidationError{
			field:  "ResourceType",
			reason: "value does not match regex pattern \"^(([a-z][a-z0-9_]{1,61}[a-z0-9]/)*[a-z][a-z0-9_]{1,62}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetOptionalResourceId()) > 1024 {
		err := RelationshipFilterValidationError{
			field:  "OptionalResourceId",
			reason: "value length must be at most 1024 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_RelationshipFilter_OptionalResourceId_Pattern.MatchString(m.GetOptionalResourceId()) {
		err := RelationshipFilterValidationError{
			field:  "OptionalResourceId",
			reason: "value does not match regex pattern \"^([a-zA-Z0-9/_|\\\\-=+]{1,})?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetOptionalResourceIdPrefix()) > 1024 {
		err := RelationshipFilterValidationError{
			field:  "OptionalResourceIdPrefix",
			reason: "value length must be at most 1024 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_RelationshipFilter_OptionalResourceIdPrefix_Pattern.MatchString(m.GetOptionalResourceIdPrefix()) {
		err := RelationshipFilterValidationError{
			field:  "OptionalResourceIdPrefix",
			reason: "value does not match regex pattern \"^([a-zA-Z0-9/_|\\\\-=+]{1,})?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetOptionalRelation()) > 64 {
		err := RelationshipFilterValidationError{
			field:  "OptionalRelation",
			reason: "value length must be at most 64 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_RelationshipFilter_OptionalRelation_Pattern.MatchString(m.GetOptionalRelation()) {
		err := RelationshipFilterValidationError{
			field:  "OptionalRelation",
			reason: "value does not match regex pattern \"^([a-z][a-z0-9_]{1,62}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOptionalSubjectFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RelationshipFilterValidationError{
					field:  "OptionalSubjectFilter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RelationshipFilterValidationError{
					field:  "OptionalSubjectFilter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOptionalSubjectFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RelationshipFilterValidationError{
				field:  "OptionalSubjectFilter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RelationshipFilterMultiError(errors)
	}

	return nil
}

// RelationshipFilterMultiError is an error wrapping multiple validation errors
// returned by RelationshipFilter.ValidateAll() if the designated constraints
// aren't met.
type RelationshipFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RelationshipFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RelationshipFilterMultiError) AllErrors() []error { return m }

// RelationshipFilterValidationError is the validation error returned by
// RelationshipFilter.Validate if the designated constraints aren't met.
type RelationshipFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RelationshipFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RelationshipFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RelationshipFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RelationshipFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RelationshipFilterValidationError) ErrorName() string {
	return "RelationshipFilterValidationError"
}

// Error satisfies the builtin error interface
func (e RelationshipFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRelationshipFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RelationshipFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RelationshipFilterValidationError{}

var _RelationshipFilter_ResourceType_Pattern = regexp.MustCompile("^(([a-z][a-z0-9_]{1,61}[a-z0-9]/)*[a-z][a-z0-9_]{1,62}[a-z0-9])?$")

var _RelationshipFilter_OptionalResourceId_Pattern = regexp.MustCompile("^([a-zA-Z0-9/_|\\-=+]{1,})?$")

var _RelationshipFilter_OptionalResourceIdPrefix_Pattern = regexp.MustCompile("^([a-zA-Z0-9/_|\\-=+]{1,})?$")

var _RelationshipFilter_OptionalRelation_Pattern = regexp.MustCompile("^([a-z][a-z0-9_]{1,62}[a-z0-9])?$")

// Validate checks the field values on SubjectFilter with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SubjectFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubjectFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SubjectFilterMultiError, or
// nil if none found.
func (m *SubjectFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *SubjectFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetSubjectType()) > 128 {
		err := SubjectFilterValidationError{
			field:  "SubjectType",
			reason: "value length must be at most 128 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SubjectFilter_SubjectType_Pattern.MatchString(m.GetSubjectType()) {
		err := SubjectFilterValidationError{
			field:  "SubjectType",
			reason: "value does not match regex pattern \"^([a-z][a-z0-9_]{1,61}[a-z0-9]/)*[a-z][a-z0-9_]{1,62}[a-z0-9]$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetOptionalSubjectId()) > 1024 {
		err := SubjectFilterValidationError{
			field:  "OptionalSubjectId",
			reason: "value length must be at most 1024 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SubjectFilter_OptionalSubjectId_Pattern.MatchString(m.GetOptionalSubjectId()) {
		err := SubjectFilterValidationError{
			field:  "OptionalSubjectId",
			reason: "value does not match regex pattern \"^(([a-zA-Z0-9/_|\\\\-=+]{1,})|\\\\*)?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOptionalRelation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubjectFilterValidationError{
					field:  "OptionalRelation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubjectFilterValidationError{
					field:  "OptionalRelation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOptionalRelation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubjectFilterValidationError{
				field:  "OptionalRelation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubjectFilterMultiError(errors)
	}

	return nil
}

// SubjectFilterMultiError is an error wrapping multiple validation errors
// returned by SubjectFilter.ValidateAll() if the designated constraints
// aren't met.
type SubjectFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubjectFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubjectFilterMultiError) AllErrors() []error { return m }

// SubjectFilterValidationError is the validation error returned by
// SubjectFilter.Validate if the designated constraints aren't met.
type SubjectFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubjectFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubjectFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubjectFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubjectFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubjectFilterValidationError) ErrorName() string { return "SubjectFilterValidationError" }

// Error satisfies the builtin error interface
func (e SubjectFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubjectFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubjectFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubjectFilterValidationError{}

var _SubjectFilter_SubjectType_Pattern = regexp.MustCompile("^([a-z][a-z0-9_]{1,61}[a-z0-9]/)*[a-z][a-z0-9_]{1,62}[a-z0-9]$")

var _SubjectFilter_OptionalSubjectId_Pattern = regexp.MustCompile("^(([a-zA-Z0-9/_|\\-=+]{1,})|\\*)?$")

// Validate checks the field values on AllowedRelation_PublicWildcard with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AllowedRelation_PublicWildcard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AllowedRelation_PublicWildcard with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AllowedRelation_PublicWildcardMultiError, or nil if none found.
func (m *AllowedRelation_PublicWildcard) ValidateAll() error {
	return m.validate(true)
}

func (m *AllowedRelation_PublicWildcard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AllowedRelation_PublicWildcardMultiError(errors)
	}

	return nil
}

// AllowedRelation_PublicWildcardMultiError is an error wrapping multiple
// validation errors returned by AllowedRelation_PublicWildcard.ValidateAll()
// if the designated constraints aren't met.
type AllowedRelation_PublicWildcardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AllowedRelation_PublicWildcardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AllowedRelation_PublicWildcardMultiError) AllErrors() []error { return m }

// AllowedRelation_PublicWildcardValidationError is the validation error
// returned by AllowedRelation_PublicWildcard.Validate if the designated
// constraints aren't met.
type AllowedRelation_PublicWildcardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AllowedRelation_PublicWildcardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AllowedRelation_PublicWildcardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AllowedRelation_PublicWildcardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AllowedRelation_PublicWildcardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AllowedRelation_PublicWildcardValidationError) ErrorName() string {
	return "AllowedRelation_PublicWildcardValidationError"
}

// Error satisfies the builtin error interface
func (e AllowedRelation_PublicWildcardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAllowedRelation_PublicWildcard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AllowedRelation_PublicWildcardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AllowedRelation_PublicWildcardValidationError{}

// Validate checks the field values on SetOperation_Child with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetOperation_Child) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetOperation_Child with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetOperation_ChildMultiError, or nil if none found.
func (m *SetOperation_Child) ValidateAll() error {
	return m.validate(true)
}

func (m *SetOperation_Child) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSourcePosition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetOperation_ChildValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetOperation_ChildValidationError{
					field:  "SourcePosition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourcePosition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetOperation_ChildValidationError{
				field:  "SourcePosition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	oneofChildTypePresent := false
	switch v := m.ChildType.(type) {
	case *SetOperation_Child_XThis:
		if v == nil {
			err := SetOperation_ChildValidationError{
				field:  "ChildType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofChildTypePresent = true

		if all {
			switch v := interface{}(m.GetXThis()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetOperation_ChildValidationError{
						field:  "XThis",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetOperation_ChildValidationError{
						field:  "XThis",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetXThis()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetOperation_ChildValidationError{
					field:  "XThis",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SetOperation_Child_ComputedUserset:
		if v == nil {
			err := SetOperation_ChildValidationError{
				field:  "ChildType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofChildTypePresent = true

		if m.GetComputedUserset() == nil {
			err := SetOperation_ChildValidationError{
				field:  "ComputedUserset",
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetComputedUserset()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetOperation_ChildValidationError{
						field:  "ComputedUserset",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetOperation_ChildValidationError{
						field:  "ComputedUserset",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetComputedUserset()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetOperation_ChildValidationError{
					field:  "ComputedUserset",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SetOperation_Child_TupleToUserset:
		if v == nil {
			err := SetOperation_ChildValidationError{
				field:  "ChildType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofChildTypePresent = true

		if m.GetTupleToUserset() == nil {
			err := SetOperation_ChildValidationError{
				field:  "TupleToUserset",
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTupleToUserset()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetOperation_ChildValidationError{
						field:  "TupleToUserset",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetOperation_ChildValidationError{
						field:  "TupleToUserset",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTupleToUserset()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetOperation_ChildValidationError{
					field:  "TupleToUserset",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SetOperation_Child_UsersetRewrite:
		if v == nil {
			err := SetOperation_ChildValidationError{
				field:  "ChildType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofChildTypePresent = true

		if m.GetUsersetRewrite() == nil {
			err := SetOperation_ChildValidationError{
				field:  "UsersetRewrite",
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUsersetRewrite()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetOperation_ChildValidationError{
						field:  "UsersetRewrite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetOperation_ChildValidationError{
						field:  "UsersetRewrite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUsersetRewrite()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetOperation_ChildValidationError{
					field:  "UsersetRewrite",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SetOperation_Child_FunctionedTupleToUserset:
		if v == nil {
			err := SetOperation_ChildValidationError{
				field:  "ChildType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofChildTypePresent = true

		if m.GetFunctionedTupleToUserset() == nil {
			err := SetOperation_ChildValidationError{
				field:  "FunctionedTupleToUserset",
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFunctionedTupleToUserset()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetOperation_ChildValidationError{
						field:  "FunctionedTupleToUserset",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetOperation_ChildValidationError{
						field:  "FunctionedTupleToUserset",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFunctionedTupleToUserset()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetOperation_ChildValidationError{
					field:  "FunctionedTupleToUserset",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SetOperation_Child_XNil:
		if v == nil {
			err := SetOperation_ChildValidationError{
				field:  "ChildType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofChildTypePresent = true

		if all {
			switch v := interface{}(m.GetXNil()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetOperation_ChildValidationError{
						field:  "XNil",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetOperation_ChildValidationError{
						field:  "XNil",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetXNil()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetOperation_ChildValidationError{
					field:  "XNil",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	if !oneofChildTypePresent {
		err := SetOperation_ChildValidationError{
			field:  "ChildType",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SetOperation_ChildMultiError(errors)
	}

	return nil
}

// SetOperation_ChildMultiError is an error wrapping multiple validation errors
// returned by SetOperation_Child.ValidateAll() if the designated constraints
// aren't met.
type SetOperation_ChildMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetOperation_ChildMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetOperation_ChildMultiError) AllErrors() []error { return m }

// SetOperation_ChildValidationError is the validation error returned by
// SetOperation_Child.Validate if the designated constraints aren't met.
type SetOperation_ChildValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetOperation_ChildValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetOperation_ChildValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetOperation_ChildValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetOperation_ChildValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetOperation_ChildValidationError) ErrorName() string {
	return "SetOperation_ChildValidationError"
}

// Error satisfies the builtin error interface
func (e SetOperation_ChildValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetOperation_Child.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetOperation_ChildValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetOperation_ChildValidationError{}

// Validate checks the field values on SetOperation_Child_This with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetOperation_Child_This) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetOperation_Child_This with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetOperation_Child_ThisMultiError, or nil if none found.
func (m *SetOperation_Child_This) ValidateAll() error {
	return m.validate(true)
}

func (m *SetOperation_Child_This) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SetOperation_Child_ThisMultiError(errors)
	}

	return nil
}

// SetOperation_Child_ThisMultiError is an error wrapping multiple validation
// errors returned by SetOperation_Child_This.ValidateAll() if the designated
// constraints aren't met.
type SetOperation_Child_ThisMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetOperation_Child_ThisMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetOperation_Child_ThisMultiError) AllErrors() []error { return m }

// SetOperation_Child_ThisValidationError is the validation error returned by
// SetOperation_Child_This.Validate if the designated constraints aren't met.
type SetOperation_Child_ThisValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetOperation_Child_ThisValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetOperation_Child_ThisValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetOperation_Child_ThisValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetOperation_Child_ThisValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetOperation_Child_ThisValidationError) ErrorName() string {
	return "SetOperation_Child_ThisValidationError"
}

// Error satisfies the builtin error interface
func (e SetOperation_Child_ThisValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetOperation_Child_This.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetOperation_Child_ThisValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetOperation_Child_ThisValidationError{}

// Validate checks the field values on SetOperation_Child_Nil with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetOperation_Child_Nil) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetOperation_Child_Nil with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetOperation_Child_NilMultiError, or nil if none found.
func (m *SetOperation_Child_Nil) ValidateAll() error {
	return m.validate(true)
}

func (m *SetOperation_Child_Nil) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SetOperation_Child_NilMultiError(errors)
	}

	return nil
}

// SetOperation_Child_NilMultiError is an error wrapping multiple validation
// errors returned by SetOperation_Child_Nil.ValidateAll() if the designated
// constraints aren't met.
type SetOperation_Child_NilMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetOperation_Child_NilMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetOperation_Child_NilMultiError) AllErrors() []error { return m }

// SetOperation_Child_NilValidationError is the validation error returned by
// SetOperation_Child_Nil.Validate if the designated constraints aren't met.
type SetOperation_Child_NilValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetOperation_Child_NilValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetOperation_Child_NilValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetOperation_Child_NilValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetOperation_Child_NilValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetOperation_Child_NilValidationError) ErrorName() string {
	return "SetOperation_Child_NilValidationError"
}

// Error satisfies the builtin error interface
func (e SetOperation_Child_NilValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetOperation_Child_Nil.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetOperation_Child_NilValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetOperation_Child_NilValidationError{}

// Validate checks the field values on TupleToUserset_Tupleset with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TupleToUserset_Tupleset) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TupleToUserset_Tupleset with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TupleToUserset_TuplesetMultiError, or nil if none found.
func (m *TupleToUserset_Tupleset) ValidateAll() error {
	return m.validate(true)
}

func (m *TupleToUserset_Tupleset) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetRelation()) > 64 {
		err := TupleToUserset_TuplesetValidationError{
			field:  "Relation",
			reason: "value length must be at most 64 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_TupleToUserset_Tupleset_Relation_Pattern.MatchString(m.GetRelation()) {
		err := TupleToUserset_TuplesetValidationError{
			field:  "Relation",
			reason: "value does not match regex pattern \"^[a-z][a-z0-9_]{1,62}[a-z0-9]$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return TupleToUserset_TuplesetMultiError(errors)
	}

	return nil
}

// TupleToUserset_TuplesetMultiError is an error wrapping multiple validation
// errors returned by TupleToUserset_Tupleset.ValidateAll() if the designated
// constraints aren't met.
type TupleToUserset_TuplesetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TupleToUserset_TuplesetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TupleToUserset_TuplesetMultiError) AllErrors() []error { return m }

// TupleToUserset_TuplesetValidationError is the validation error returned by
// TupleToUserset_Tupleset.Validate if the designated constraints aren't met.
type TupleToUserset_TuplesetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TupleToUserset_TuplesetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TupleToUserset_TuplesetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TupleToUserset_TuplesetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TupleToUserset_TuplesetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TupleToUserset_TuplesetValidationError) ErrorName() string {
	return "TupleToUserset_TuplesetValidationError"
}

// Error satisfies the builtin error interface
func (e TupleToUserset_TuplesetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTupleToUserset_Tupleset.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TupleToUserset_TuplesetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TupleToUserset_TuplesetValidationError{}

var _TupleToUserset_Tupleset_Relation_Pattern = regexp.MustCompile("^[a-z][a-z0-9_]{1,62}[a-z0-9]$")

// Validate checks the field values on FunctionedTupleToUserset_Tupleset with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FunctionedTupleToUserset_Tupleset) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FunctionedTupleToUserset_Tupleset
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FunctionedTupleToUserset_TuplesetMultiError, or nil if none found.
func (m *FunctionedTupleToUserset_Tupleset) ValidateAll() error {
	return m.validate(true)
}

func (m *FunctionedTupleToUserset_Tupleset) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetRelation()) > 64 {
		err := FunctionedTupleToUserset_TuplesetValidationError{
			field:  "Relation",
			reason: "value length must be at most 64 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_FunctionedTupleToUserset_Tupleset_Relation_Pattern.MatchString(m.GetRelation()) {
		err := FunctionedTupleToUserset_TuplesetValidationError{
			field:  "Relation",
			reason: "value does not match regex pattern \"^[a-z][a-z0-9_]{1,62}[a-z0-9]$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FunctionedTupleToUserset_TuplesetMultiError(errors)
	}

	return nil
}

// FunctionedTupleToUserset_TuplesetMultiError is an error wrapping multiple
// validation errors returned by
// FunctionedTupleToUserset_Tupleset.ValidateAll() if the designated
// constraints aren't met.
type FunctionedTupleToUserset_TuplesetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FunctionedTupleToUserset_TuplesetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FunctionedTupleToUserset_TuplesetMultiError) AllErrors() []error { return m }

// FunctionedTupleToUserset_TuplesetValidationError is the validation error
// returned by FunctionedTupleToUserset_Tupleset.Validate if the designated
// constraints aren't met.
type FunctionedTupleToUserset_TuplesetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FunctionedTupleToUserset_TuplesetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FunctionedTupleToUserset_TuplesetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FunctionedTupleToUserset_TuplesetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FunctionedTupleToUserset_TuplesetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FunctionedTupleToUserset_TuplesetValidationError) ErrorName() string {
	return "FunctionedTupleToUserset_TuplesetValidationError"
}

// Error satisfies the builtin error interface
func (e FunctionedTupleToUserset_TuplesetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFunctionedTupleToUserset_Tupleset.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FunctionedTupleToUserset_TuplesetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FunctionedTupleToUserset_TuplesetValidationError{}

var _FunctionedTupleToUserset_Tupleset_Relation_Pattern = regexp.MustCompile("^[a-z][a-z0-9_]{1,62}[a-z0-9]$")

// Validate checks the field values on SubjectFilter_RelationFilter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubjectFilter_RelationFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubjectFilter_RelationFilter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubjectFilter_RelationFilterMultiError, or nil if none found.
func (m *SubjectFilter_RelationFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *SubjectFilter_RelationFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetRelation()) > 64 {
		err := SubjectFilter_RelationFilterValidationError{
			field:  "Relation",
			reason: "value length must be at most 64 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SubjectFilter_RelationFilter_Relation_Pattern.MatchString(m.GetRelation()) {
		err := SubjectFilter_RelationFilterValidationError{
			field:  "Relation",
			reason: "value does not match regex pattern \"^([a-z][a-z0-9_]{1,62}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SubjectFilter_RelationFilterMultiError(errors)
	}

	return nil
}

// SubjectFilter_RelationFilterMultiError is an error wrapping multiple
// validation errors returned by SubjectFilter_RelationFilter.ValidateAll() if
// the designated constraints aren't met.
type SubjectFilter_RelationFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubjectFilter_RelationFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubjectFilter_RelationFilterMultiError) AllErrors() []error { return m }

// SubjectFilter_RelationFilterValidationError is the validation error returned
// by SubjectFilter_RelationFilter.Validate if the designated constraints
// aren't met.
type SubjectFilter_RelationFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubjectFilter_RelationFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubjectFilter_RelationFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubjectFilter_RelationFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubjectFilter_RelationFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubjectFilter_RelationFilterValidationError) ErrorName() string {
	return "SubjectFilter_RelationFilterValidationError"
}

// Error satisfies the builtin error interface
func (e SubjectFilter_RelationFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubjectFilter_RelationFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubjectFilter_RelationFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubjectFilter_RelationFilterValidationError{}

var _SubjectFilter_RelationFilter_Relation_Pattern = regexp.MustCompile("^([a-z][a-z0-9_]{1,62}[a-z0-9])?$")
