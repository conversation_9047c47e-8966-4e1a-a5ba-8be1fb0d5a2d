---
name: "Pull Request Labeler"
on:  # yamllint disable-line rule:truthy
  pull_request_target:
  merge_group:
    types:
      - "checks_requested"
permissions:
  contents: "read"
jobs:
  triage:
    runs-on: "depot-ubuntu-24.04-small"
    permissions:
      pull-requests: "write"
    steps:
      - uses: "actions/labeler@8558fd74291d67161a8a78ce36a881fa63b766a9" # v5.0.0
        with:
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
          sync-labels: true
