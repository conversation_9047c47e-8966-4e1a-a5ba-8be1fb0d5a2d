// Code generated by github.com/ecordell/optgen. DO NOT EDIT.
package options

import (
	defaults "github.com/creasty/defaults"
	helpers "github.com/ecordell/optgen/helpers"
	"time"
)

type ExperimentalServerOptionsOption func(e *ExperimentalServerOptions)

// NewExperimentalServerOptionsWithOptions creates a new ExperimentalServerOptions with the passed in options set
func NewExperimentalServerOptionsWithOptions(opts ...ExperimentalServerOptionsOption) *ExperimentalServerOptions {
	e := &ExperimentalServerOptions{}
	for _, o := range opts {
		o(e)
	}
	return e
}

// NewExperimentalServerOptionsWithOptionsAndDefaults creates a new ExperimentalServerOptions with the passed in options set starting from the defaults
func NewExperimentalServerOptionsWithOptionsAndDefaults(opts ...ExperimentalServerOptionsOption) *ExperimentalServerOptions {
	e := &ExperimentalServerOptions{}
	defaults.MustSet(e)
	for _, o := range opts {
		o(e)
	}
	return e
}

// ToOption returns a new ExperimentalServerOptionsOption that sets the values from the passed in ExperimentalServerOptions
func (e *ExperimentalServerOptions) ToOption() ExperimentalServerOptionsOption {
	return func(to *ExperimentalServerOptions) {
		to.StreamReadTimeout = e.StreamReadTimeout
		to.DefaultExportBatchSize = e.DefaultExportBatchSize
		to.MaxExportBatchSize = e.MaxExportBatchSize
		to.BulkCheckMaxConcurrency = e.BulkCheckMaxConcurrency
	}
}

// DebugMap returns a map form of ExperimentalServerOptions for debugging
func (e ExperimentalServerOptions) DebugMap() map[string]any {
	debugMap := map[string]any{}
	debugMap["StreamReadTimeout"] = helpers.DebugValue(e.StreamReadTimeout, false)
	debugMap["DefaultExportBatchSize"] = helpers.DebugValue(e.DefaultExportBatchSize, false)
	debugMap["MaxExportBatchSize"] = helpers.DebugValue(e.MaxExportBatchSize, false)
	debugMap["BulkCheckMaxConcurrency"] = helpers.DebugValue(e.BulkCheckMaxConcurrency, false)
	return debugMap
}

// ExperimentalServerOptionsWithOptions configures an existing ExperimentalServerOptions with the passed in options set
func ExperimentalServerOptionsWithOptions(e *ExperimentalServerOptions, opts ...ExperimentalServerOptionsOption) *ExperimentalServerOptions {
	for _, o := range opts {
		o(e)
	}
	return e
}

// WithOptions configures the receiver ExperimentalServerOptions with the passed in options set
func (e *ExperimentalServerOptions) WithOptions(opts ...ExperimentalServerOptionsOption) *ExperimentalServerOptions {
	for _, o := range opts {
		o(e)
	}
	return e
}

// WithStreamReadTimeout returns an option that can set StreamReadTimeout on a ExperimentalServerOptions
func WithStreamReadTimeout(streamReadTimeout time.Duration) ExperimentalServerOptionsOption {
	return func(e *ExperimentalServerOptions) {
		e.StreamReadTimeout = streamReadTimeout
	}
}

// WithDefaultExportBatchSize returns an option that can set DefaultExportBatchSize on a ExperimentalServerOptions
func WithDefaultExportBatchSize(defaultExportBatchSize uint32) ExperimentalServerOptionsOption {
	return func(e *ExperimentalServerOptions) {
		e.DefaultExportBatchSize = defaultExportBatchSize
	}
}

// WithMaxExportBatchSize returns an option that can set MaxExportBatchSize on a ExperimentalServerOptions
func WithMaxExportBatchSize(maxExportBatchSize uint32) ExperimentalServerOptionsOption {
	return func(e *ExperimentalServerOptions) {
		e.MaxExportBatchSize = maxExportBatchSize
	}
}

// WithBulkCheckMaxConcurrency returns an option that can set BulkCheckMaxConcurrency on a ExperimentalServerOptions
func WithBulkCheckMaxConcurrency(bulkCheckMaxConcurrency uint16) ExperimentalServerOptionsOption {
	return func(e *ExperimentalServerOptions) {
		e.BulkCheckMaxConcurrency = bulkCheckMaxConcurrency
	}
}
