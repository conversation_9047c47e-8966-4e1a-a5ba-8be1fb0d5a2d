// Code generated by github.com/ecordell/optgen. DO NOT EDIT.
package common

import (
	squirrel "github.com/Masterminds/squirrel"
	defaults "github.com/creasty/defaults"
	helpers "github.com/ecordell/optgen/helpers"
)

type SchemaInformationOption func(s *SchemaInformation)

// NewSchemaInformationWithOptions creates a new SchemaInformation with the passed in options set
func NewSchemaInformationWithOptions(opts ...SchemaInformationOption) *SchemaInformation {
	s := &SchemaInformation{}
	for _, o := range opts {
		o(s)
	}
	return s
}

// NewSchemaInformationWithOptionsAndDefaults creates a new SchemaInformation with the passed in options set starting from the defaults
func NewSchemaInformationWithOptionsAndDefaults(opts ...SchemaInformationOption) *SchemaInformation {
	s := &SchemaInformation{}
	defaults.MustSet(s)
	for _, o := range opts {
		o(s)
	}
	return s
}

// ToOption returns a new SchemaInformationOption that sets the values from the passed in SchemaInformation
func (s *SchemaInformation) ToOption() SchemaInformationOption {
	return func(to *SchemaInformation) {
		to.RelationshipTableName = s.RelationshipTableName
		to.ColNamespace = s.ColNamespace
		to.ColObjectID = s.ColObjectID
		to.ColRelation = s.ColRelation
		to.ColUsersetNamespace = s.ColUsersetNamespace
		to.ColUsersetObjectID = s.ColUsersetObjectID
		to.ColUsersetRelation = s.ColUsersetRelation
		to.ColCaveatName = s.ColCaveatName
		to.ColCaveatContext = s.ColCaveatContext
		to.ColExpiration = s.ColExpiration
		to.ColIntegrityKeyID = s.ColIntegrityKeyID
		to.ColIntegrityHash = s.ColIntegrityHash
		to.ColIntegrityTimestamp = s.ColIntegrityTimestamp
		to.Indexes = s.Indexes
		to.PaginationFilterType = s.PaginationFilterType
		to.PlaceholderFormat = s.PlaceholderFormat
		to.NowFunction = s.NowFunction
		to.ColumnOptimization = s.ColumnOptimization
		to.IntegrityEnabled = s.IntegrityEnabled
		to.ExpirationDisabled = s.ExpirationDisabled
		to.SortByResourceColumnOrder = s.SortByResourceColumnOrder
		to.SortBySubjectColumnOrder = s.SortBySubjectColumnOrder
	}
}

// DebugMap returns a map form of SchemaInformation for debugging
func (s SchemaInformation) DebugMap() map[string]any {
	debugMap := map[string]any{}
	debugMap["RelationshipTableName"] = helpers.DebugValue(s.RelationshipTableName, false)
	debugMap["ColNamespace"] = helpers.DebugValue(s.ColNamespace, false)
	debugMap["ColObjectID"] = helpers.DebugValue(s.ColObjectID, false)
	debugMap["ColRelation"] = helpers.DebugValue(s.ColRelation, false)
	debugMap["ColUsersetNamespace"] = helpers.DebugValue(s.ColUsersetNamespace, false)
	debugMap["ColUsersetObjectID"] = helpers.DebugValue(s.ColUsersetObjectID, false)
	debugMap["ColUsersetRelation"] = helpers.DebugValue(s.ColUsersetRelation, false)
	debugMap["ColCaveatName"] = helpers.DebugValue(s.ColCaveatName, false)
	debugMap["ColCaveatContext"] = helpers.DebugValue(s.ColCaveatContext, false)
	debugMap["ColExpiration"] = helpers.DebugValue(s.ColExpiration, false)
	debugMap["ColIntegrityKeyID"] = helpers.DebugValue(s.ColIntegrityKeyID, false)
	debugMap["ColIntegrityHash"] = helpers.DebugValue(s.ColIntegrityHash, false)
	debugMap["ColIntegrityTimestamp"] = helpers.DebugValue(s.ColIntegrityTimestamp, false)
	debugMap["Indexes"] = helpers.DebugValue(s.Indexes, false)
	debugMap["PaginationFilterType"] = helpers.DebugValue(s.PaginationFilterType, false)
	debugMap["PlaceholderFormat"] = helpers.DebugValue(s.PlaceholderFormat, false)
	debugMap["NowFunction"] = helpers.DebugValue(s.NowFunction, false)
	debugMap["ColumnOptimization"] = helpers.DebugValue(s.ColumnOptimization, false)
	debugMap["IntegrityEnabled"] = helpers.DebugValue(s.IntegrityEnabled, false)
	debugMap["ExpirationDisabled"] = helpers.DebugValue(s.ExpirationDisabled, false)
	debugMap["SortByResourceColumnOrder"] = helpers.DebugValue(s.SortByResourceColumnOrder, false)
	debugMap["SortBySubjectColumnOrder"] = helpers.DebugValue(s.SortBySubjectColumnOrder, false)
	return debugMap
}

// SchemaInformationWithOptions configures an existing SchemaInformation with the passed in options set
func SchemaInformationWithOptions(s *SchemaInformation, opts ...SchemaInformationOption) *SchemaInformation {
	for _, o := range opts {
		o(s)
	}
	return s
}

// WithOptions configures the receiver SchemaInformation with the passed in options set
func (s *SchemaInformation) WithOptions(opts ...SchemaInformationOption) *SchemaInformation {
	for _, o := range opts {
		o(s)
	}
	return s
}

// WithRelationshipTableName returns an option that can set RelationshipTableName on a SchemaInformation
func WithRelationshipTableName(relationshipTableName string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.RelationshipTableName = relationshipTableName
	}
}

// WithColNamespace returns an option that can set ColNamespace on a SchemaInformation
func WithColNamespace(colNamespace string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.ColNamespace = colNamespace
	}
}

// WithColObjectID returns an option that can set ColObjectID on a SchemaInformation
func WithColObjectID(colObjectID string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.ColObjectID = colObjectID
	}
}

// WithColRelation returns an option that can set ColRelation on a SchemaInformation
func WithColRelation(colRelation string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.ColRelation = colRelation
	}
}

// WithColUsersetNamespace returns an option that can set ColUsersetNamespace on a SchemaInformation
func WithColUsersetNamespace(colUsersetNamespace string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.ColUsersetNamespace = colUsersetNamespace
	}
}

// WithColUsersetObjectID returns an option that can set ColUsersetObjectID on a SchemaInformation
func WithColUsersetObjectID(colUsersetObjectID string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.ColUsersetObjectID = colUsersetObjectID
	}
}

// WithColUsersetRelation returns an option that can set ColUsersetRelation on a SchemaInformation
func WithColUsersetRelation(colUsersetRelation string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.ColUsersetRelation = colUsersetRelation
	}
}

// WithColCaveatName returns an option that can set ColCaveatName on a SchemaInformation
func WithColCaveatName(colCaveatName string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.ColCaveatName = colCaveatName
	}
}

// WithColCaveatContext returns an option that can set ColCaveatContext on a SchemaInformation
func WithColCaveatContext(colCaveatContext string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.ColCaveatContext = colCaveatContext
	}
}

// WithColExpiration returns an option that can set ColExpiration on a SchemaInformation
func WithColExpiration(colExpiration string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.ColExpiration = colExpiration
	}
}

// WithColIntegrityKeyID returns an option that can set ColIntegrityKeyID on a SchemaInformation
func WithColIntegrityKeyID(colIntegrityKeyID string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.ColIntegrityKeyID = colIntegrityKeyID
	}
}

// WithColIntegrityHash returns an option that can set ColIntegrityHash on a SchemaInformation
func WithColIntegrityHash(colIntegrityHash string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.ColIntegrityHash = colIntegrityHash
	}
}

// WithColIntegrityTimestamp returns an option that can set ColIntegrityTimestamp on a SchemaInformation
func WithColIntegrityTimestamp(colIntegrityTimestamp string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.ColIntegrityTimestamp = colIntegrityTimestamp
	}
}

// WithIndexes returns an option that can append Indexess to SchemaInformation.Indexes
func WithIndexes(indexes IndexDefinition) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.Indexes = append(s.Indexes, indexes)
	}
}

// SetIndexes returns an option that can set Indexes on a SchemaInformation
func SetIndexes(indexes []IndexDefinition) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.Indexes = indexes
	}
}

// WithPaginationFilterType returns an option that can set PaginationFilterType on a SchemaInformation
func WithPaginationFilterType(paginationFilterType PaginationFilterType) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.PaginationFilterType = paginationFilterType
	}
}

// WithPlaceholderFormat returns an option that can set PlaceholderFormat on a SchemaInformation
func WithPlaceholderFormat(placeholderFormat squirrel.PlaceholderFormat) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.PlaceholderFormat = placeholderFormat
	}
}

// WithNowFunction returns an option that can set NowFunction on a SchemaInformation
func WithNowFunction(nowFunction string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.NowFunction = nowFunction
	}
}

// WithColumnOptimization returns an option that can set ColumnOptimization on a SchemaInformation
func WithColumnOptimization(columnOptimization ColumnOptimizationOption) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.ColumnOptimization = columnOptimization
	}
}

// WithIntegrityEnabled returns an option that can set IntegrityEnabled on a SchemaInformation
func WithIntegrityEnabled(integrityEnabled bool) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.IntegrityEnabled = integrityEnabled
	}
}

// WithExpirationDisabled returns an option that can set ExpirationDisabled on a SchemaInformation
func WithExpirationDisabled(expirationDisabled bool) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.ExpirationDisabled = expirationDisabled
	}
}

// WithSortByResourceColumnOrder returns an option that can append SortByResourceColumnOrders to SchemaInformation.SortByResourceColumnOrder
func WithSortByResourceColumnOrder(sortByResourceColumnOrder string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.SortByResourceColumnOrder = append(s.SortByResourceColumnOrder, sortByResourceColumnOrder)
	}
}

// SetSortByResourceColumnOrder returns an option that can set SortByResourceColumnOrder on a SchemaInformation
func SetSortByResourceColumnOrder(sortByResourceColumnOrder []string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.SortByResourceColumnOrder = sortByResourceColumnOrder
	}
}

// WithSortBySubjectColumnOrder returns an option that can append SortBySubjectColumnOrders to SchemaInformation.SortBySubjectColumnOrder
func WithSortBySubjectColumnOrder(sortBySubjectColumnOrder string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.SortBySubjectColumnOrder = append(s.SortBySubjectColumnOrder, sortBySubjectColumnOrder)
	}
}

// SetSortBySubjectColumnOrder returns an option that can set SortBySubjectColumnOrder on a SchemaInformation
func SetSortBySubjectColumnOrder(sortBySubjectColumnOrder []string) SchemaInformationOption {
	return func(s *SchemaInformation) {
		s.SortBySubjectColumnOrder = sortBySubjectColumnOrder
	}
}
