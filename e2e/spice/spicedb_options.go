// Code generated by github.com/ecordell/optgen. DO NOT EDIT.
package spice

import "context"

type NodeOption func(n *Node)

// NewNodeWithOptions creates a new Node with the passed in options set
func NewNodeWithOptions(opts ...NodeOption) *Node {
	n := &Node{}
	for _, o := range opts {
		o(n)
	}
	return n
}

// ToOption returns a new NodeOption that sets the values from the passed in Node
func (n *Node) ToOption() NodeOption {
	return func(to *Node) {
		to.ID = n.ID
		to.PresharedKey = n.PresharedKey
		to.Datastore = n.Datastore
		to.DBName = n.DBName
		to.URI = n.URI
		to.GrpcPort = n.GrpcPort
		to.HTTPPort = n.HTTPPort
		to.DispatchPort = n.DispatchPort
		to.MetricsPort = n.MetricsPort
		to.HedgingEnabled = n.HedgingEnabled
		to.Pid = n.Pid
		to.Cancel = n.Cancel
		to.client = n.client
	}
}

// NodeWithOptions configures an existing Node with the passed in options set
func NodeWithOptions(n *Node, opts ...NodeOption) *Node {
	for _, o := range opts {
		o(n)
	}
	return n
}

// WithID returns an option that can set ID on a Node
func WithID(iD string) NodeOption {
	return func(n *Node) {
		n.ID = iD
	}
}

// WithPresharedKey returns an option that can set PresharedKey on a Node
func WithPresharedKey(presharedKey string) NodeOption {
	return func(n *Node) {
		n.PresharedKey = presharedKey
	}
}

// WithDatastore returns an option that can set Datastore on a Node
func WithDatastore(datastore string) NodeOption {
	return func(n *Node) {
		n.Datastore = datastore
	}
}

// WithDBName returns an option that can set DBName on a Node
func WithDBName(dBName string) NodeOption {
	return func(n *Node) {
		n.DBName = dBName
	}
}

// WithURI returns an option that can set URI on a Node
func WithURI(uRI string) NodeOption {
	return func(n *Node) {
		n.URI = uRI
	}
}

// WithGrpcPort returns an option that can set GrpcPort on a Node
func WithGrpcPort(grpcPort int) NodeOption {
	return func(n *Node) {
		n.GrpcPort = grpcPort
	}
}

// WithHTTPPort returns an option that can set HTTPPort on a Node
func WithHTTPPort(hTTPPort int) NodeOption {
	return func(n *Node) {
		n.HTTPPort = hTTPPort
	}
}

// WithDispatchPort returns an option that can set DispatchPort on a Node
func WithDispatchPort(dispatchPort int) NodeOption {
	return func(n *Node) {
		n.DispatchPort = dispatchPort
	}
}

// WithMetricsPort returns an option that can set MetricsPort on a Node
func WithMetricsPort(metricsPort int) NodeOption {
	return func(n *Node) {
		n.MetricsPort = metricsPort
	}
}

// WithHedgingEnabled returns an option that can set HedgingEnabled on a Node
func WithHedgingEnabled(hedgingEnabled bool) NodeOption {
	return func(n *Node) {
		n.HedgingEnabled = hedgingEnabled
	}
}

// WithPid returns an option that can set Pid on a Node
func WithPid(pid int) NodeOption {
	return func(n *Node) {
		n.Pid = pid
	}
}

// WithCancel returns an option that can set Cancel on a Node
func WithCancel(cancel context.CancelFunc) NodeOption {
	return func(n *Node) {
		n.Cancel = cancel
	}
}
