// Code generated by github.com/ecordell/optgen. DO NOT EDIT.
package cockroach

type CockroachOption func(c *Node)

// NewCockroachWithOptions creates a new Node with the passed in options set
func NewCockroachWithOptions(opts ...CockroachOption) *Node {
	c := &Node{}
	for _, o := range opts {
		o(c)
	}
	return c
}

// CockroachWithOptions configures an existing Node with the passed in options set
func CockroachWithOptions(c *Node, opts ...CockroachOption) *Node {
	for _, o := range opts {
		o(c)
	}
	return c
}

// WithPeers returns an option that can append Peerss to Node.Peers
func WithPeers(peers string) CockroachOption {
	return func(c *Node) {
		c.Peers = append(c.Peers, peers)
	}
}

// SetPeers returns an option that can set <PERSON><PERSON> on a Node
func SetPeers(peers []string) CockroachOption {
	return func(c *Node) {
		c.Peers = peers
	}
}

// WithAddr returns an option that can set <PERSON>dr on a Node
func WithAddr(addr string) CockroachOption {
	return func(c *Node) {
		c.Addr = addr
	}
}

// WithHttpaddr returns an option that can set <PERSON>ttpaddr on a Node
func WithHttpaddr(httpaddr string) CockroachOption {
	return func(c *Node) {
		c.Httpaddr = httpaddr
	}
}

// WithId returns an option that can set ID on a Node
func WithId(id string) CockroachOption {
	return func(c *Node) {
		c.ID = id
	}
}
